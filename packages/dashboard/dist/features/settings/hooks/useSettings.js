/**
 * useSettings Hook
 *
 * Custom hook for managing application settings
 */
import { useState } from 'react';
import { useTheme } from '@adhd-trading-dashboard/shared';
/**
 * useSettings Hook
 *
 * Provides state management and handlers for user settings
 */
export const useSettings = () => {
  const { theme, setTheme } = useTheme();
  const [settings, setSettings] = useState({
    theme: theme.name,
    refreshInterval: 5,
    showNotifications: true,
    enableAdvancedMetrics: false,
    autoSaveJournal: true,
  });
  /**
   * Handle setting changes
   */
  const handleChange = (name, value) => {
    setSettings(prev => ({
      ...prev,
      [name]: value,
    }));
    // Apply theme change immediately
    if (name === 'theme') {
      setTheme(value);
    }
  };
  /**
   * Save settings
   */
  const handleSave = () => {
    console.log('Settings saved:', settings);
    // In a real app, you would call an API to save the settings here
    // Could add notification logic here
  };
  return {
    settings,
    handleChange,
    handleSave,
  };
};
//# sourceMappingURL=useSettings.js.map
