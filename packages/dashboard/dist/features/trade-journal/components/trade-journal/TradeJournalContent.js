import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from 'react/jsx-runtime';
import styled from 'styled-components';
import TradeList from '../TradeList';
import TradeJournalFilters from './TradeJournalFilters';
const ContentSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;
const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.danger};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
/**
 * Trade Journal Content Component
 */
const TradeJournalContent = ({
  error,
  showFilters,
  filteredTrades,
  isLoading,
  filters,
  handleFilterChange,
  resetFilters,
  uniqueSetups,
  uniqueModelTypes,
  uniquePrimarySetupTypes,
  uniqueSecondarySetupTypes,
  uniqueLiquidityTypes,
  uniqueDOLTypes,
}) => {
  return _jsxs(_Fragment, {
    children: [
      error && _jsx(ErrorMessage, { children: error }),
      _jsxs(ContentSection, {
        children: [
          _jsx(SectionTitle, { children: 'Recent Trades' }),
          showFilters &&
            _jsx(TradeJournalFilters, {
              filters: filters,
              handleFilterChange: handleFilterChange,
              resetFilters: resetFilters,
              uniqueSetups: uniqueSetups,
              uniqueModelTypes: uniqueModelTypes,
              uniquePrimarySetupTypes: uniquePrimarySetupTypes,
              uniqueSecondarySetupTypes: uniqueSecondarySetupTypes,
              uniqueLiquidityTypes: uniqueLiquidityTypes,
              uniqueDOLTypes: uniqueDOLTypes,
            }),
          _jsx(TradeList, { trades: filteredTrades, isLoading: isLoading, expandable: true }),
        ],
      }),
    ],
  });
};
export default TradeJournalContent;
//# sourceMappingURL=TradeJournalContent.js.map
