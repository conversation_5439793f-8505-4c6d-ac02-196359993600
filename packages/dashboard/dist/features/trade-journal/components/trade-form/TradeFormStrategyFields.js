import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
import { SetupBuilder } from '@adhd-trading-dashboard/shared';
import { MODEL_TYPE_OPTIONS } from '../../hooks';
import { SelectDropdown } from '@adhd-trading-dashboard/shared';
const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const TextArea = styled.textarea`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-height: 100px;

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;
`;
const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
/**
 * Trade Form Strategy Fields Component
 */
const TradeFormStrategyFields = ({
  formValues,
  handleChange,
  // validationErrors, // Removed as unused
  setFormValues,
}) => {
  return _jsxs(_Fragment, {
    children: [
      _jsx(SectionTitle, { children: 'Core Strategy' }),
      _jsx(FormRow, {
        children: _jsx(FormGroup, {
          children: _jsx(SelectDropdown, {
            id: 'modelType',
            name: 'modelType',
            label: 'Trading Model',
            value: formValues.modelType || '',
            onChange: handleChange,
            options: MODEL_TYPE_OPTIONS,
            placeholder: 'Select Trading Model',
          }),
        }),
      }),
      _jsx(Divider, {}),
      setFormValues &&
        _jsxs(_Fragment, {
          children: [
            _jsx(SectionTitle, { children: 'Setup Construction' }),
            _jsx(SetupBuilder, {
              onSetupChange: components => {
                setFormValues(prev => ({
                  ...prev,
                  setupComponents: components,
                }));
              },
              initialComponents: formValues.setupComponents,
            }),
            _jsx(Divider, {}),
          ],
        }),
      _jsx(SectionTitle, { children: 'Strategy Notes' }),
      _jsxs(FormGroup, {
        children: [
          _jsx(Label, { htmlFor: 'notes', children: 'Trade Notes & Strategy Details' }),
          _jsx(TextArea, {
            id: 'notes',
            name: 'notes',
            value: formValues.notes || '',
            onChange: handleChange,
            placeholder:
              'Add notes about your trading strategy, setup reasoning, market conditions, etc...',
          }),
        ],
      }),
    ],
  });
};
export default TradeFormStrategyFields;
//# sourceMappingURL=TradeFormStrategyFields.js.map
