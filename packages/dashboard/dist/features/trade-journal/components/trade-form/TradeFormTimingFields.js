import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
import { MARKET_OPTIONS } from '../../hooks';
import {
  TimePicker,
  SelectDropdown,
  Input,
  HierarchicalSessionSelector,
  // SessionUtils, // Removed as unused
} from '@adhd-trading-dashboard/shared';
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;
const TimingSection = styled.div`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;
const TimingSectionHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;
const TimingSectionTitleRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const TimingSectionIcon = styled.div`
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
`;
const TimingSectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const TimingSectionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;
const TimingSectionContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const HelpText = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  line-height: 1.4;
`;
const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-weight: 500;
`;
/**
 * Trade Form Timing Fields Component
 */
const TradeFormTimingFields = ({ formValues, handleChange, validationErrors }) => {
  return _jsx(Container, {
    children: _jsxs(TimingSection, {
      children: [
        _jsx(TimingSectionHeader, {
          children: _jsxs(TimingSectionTitleRow, {
            children: [
              _jsx(TimingSectionIcon, { children: '\uD83D\uDCC5' }),
              _jsxs('div', {
                children: [
                  _jsx(TimingSectionTitle, { children: 'Trade Timing & Execution' }),
                  _jsx(TimingSectionDescription, {
                    children: 'Complete timing details from entry to exit including market context',
                  }),
                ],
              }),
            ],
          }),
        }),
        _jsxs(TimingSectionContent, {
          children: [
            _jsxs(FormRow, {
              children: [
                _jsxs(FormGroup, {
                  children: [
                    _jsx(Label, { htmlFor: 'entryDate', children: 'Entry Date' }),
                    _jsx(Input, {
                      id: 'entryDate',
                      name: 'entryDate',
                      type: 'date',
                      value: formValues.date || '',
                      onChange: value => {
                        const event = {
                          target: { name: 'entryDate', value },
                        };
                        handleChange(event);
                      },
                    }),
                    validationErrors.entryDate &&
                      _jsx(ValidationError, { children: validationErrors.entryDate }),
                    _jsx(HelpText, { children: 'Date when the trade was entered' }),
                  ],
                }),
                _jsxs(FormGroup, {
                  children: [
                    _jsx(Label, { htmlFor: 'entryTime', children: 'Entry Time' }),
                    _jsx(TimePicker, {
                      id: 'entryTime',
                      name: 'entryTime',
                      value: formValues.entryTime || '',
                      onChange: handleChange,
                    }),
                    validationErrors.entryTime &&
                      _jsx(ValidationError, { children: validationErrors.entryTime }),
                    _jsx(HelpText, { children: 'Exact time of trade entry' }),
                  ],
                }),
                _jsxs(FormGroup, {
                  children: [
                    _jsx(Label, { htmlFor: 'market', children: 'Market' }),
                    _jsx(SelectDropdown, {
                      id: 'market',
                      name: 'market',
                      value: formValues.market || 'Stocks',
                      onChange: handleChange,
                      options: MARKET_OPTIONS,
                    }),
                    _jsx(HelpText, { children: 'Market type being traded' }),
                  ],
                }),
              ],
            }),
            _jsxs(FormRow, {
              children: [
                _jsxs(FormGroup, {
                  children: [
                    _jsx(Label, { htmlFor: 'rdTime', children: 'RD Formation Time' }),
                    _jsx(TimePicker, {
                      id: 'rdTime',
                      name: 'rdTime',
                      value: formValues.rdTime || '',
                      onChange: handleChange,
                    }),
                    validationErrors.rdTime &&
                      _jsx(ValidationError, { children: validationErrors.rdTime }),
                    _jsx(HelpText, {
                      children: 'Time when the Relative Divergence (RD) pattern formed',
                    }),
                  ],
                }),
                _jsxs(FormGroup, {
                  children: [
                    _jsx(Label, { htmlFor: 'exitTime', children: 'Exit Time' }),
                    _jsx(TimePicker, {
                      id: 'exitTime',
                      name: 'exitTime',
                      value: formValues.exitTime || '',
                      onChange: handleChange,
                    }),
                    validationErrors.exitTime &&
                      _jsx(ValidationError, { children: validationErrors.exitTime }),
                    _jsx(HelpText, { children: 'Time when trade was exited' }),
                  ],
                }),
              ],
            }),
            _jsx(FormRow, {
              children: _jsxs(FormGroup, {
                children: [
                  _jsx(Label, { htmlFor: 'session', children: 'Trading Session' }),
                  _jsx(HierarchicalSessionSelector, {
                    value: formValues.session || { session: '', macro: '' },
                    onChange: selection => {
                      const value =
                        typeof selection === 'string'
                          ? selection
                          : `${selection.session}-${selection.macro}`;
                      const event = {
                        target: { name: 'session', value },
                      };
                      handleChange(event);
                    },
                  }),
                  _jsx(HelpText, { children: 'Trading session and macro period' }),
                ],
              }),
            }),
          ],
        }),
      ],
    }),
  });
};
export default TradeFormTimingFields;
//# sourceMappingURL=TradeFormTimingFields.js.map
