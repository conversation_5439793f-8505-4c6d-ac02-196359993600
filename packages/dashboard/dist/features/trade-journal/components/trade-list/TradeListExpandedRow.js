import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
const ExpandedContent = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const ExpandedSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;
const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;
const DetailLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const DetailValue = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const ActionButton = styled(Link)`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  text-decoration: none;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;
/**
 * Trade List Expanded Row Component
 */
const TradeListExpandedRow = ({ trade }) => {
  return _jsxs(ExpandedContent, {
    children: [
      _jsxs(ExpandedSection, {
        children: [
          _jsx(SectionTitle, { children: 'Trade Details' }),
          _jsxs(DetailGrid, {
            children: [
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Market' }),
                  _jsx(DetailValue, { children: trade.trade.market || 'N/A' }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Date' }),
                  _jsx(DetailValue, { children: trade.trade.date }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Direction' }),
                  _jsx(DetailValue, { children: trade.trade.direction }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Entry Price' }),
                  _jsxs(DetailValue, {
                    children: ['$', (trade.trade.entry_price || 0).toFixed(2)],
                  }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Exit Price' }),
                  _jsxs(DetailValue, { children: ['$', (trade.trade.exit_price || 0).toFixed(2)] }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Contracts' }),
                  _jsx(DetailValue, { children: trade.trade.no_of_contracts || 0 }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'Profit/Loss' }),
                  _jsxs(DetailValue, {
                    style: {
                      color:
                        (trade.trade.achieved_pl || 0) > 0
                          ? 'green'
                          : (trade.trade.achieved_pl || 0) < 0
                          ? 'red'
                          : 'inherit',
                    },
                    children: ['$', (trade.trade.achieved_pl || 0).toFixed(2)],
                  }),
                ],
              }),
              _jsxs(DetailItem, {
                children: [
                  _jsx(DetailLabel, { children: 'R-Multiple' }),
                  _jsx(DetailValue, { children: trade.trade.r_multiple?.toFixed(2) || 'N/A' }),
                ],
              }),
            ],
          }),
        ],
      }),
      trade.setup &&
        _jsxs(ExpandedSection, {
          children: [
            _jsx(SectionTitle, { children: 'Strategy' }),
            _jsxs(DetailGrid, {
              children: [
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Model Type' }),
                    _jsx(DetailValue, { children: trade.trade.model_type || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Session' }),
                    _jsx(DetailValue, { children: trade.trade.session || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Primary Setup' }),
                    _jsx(DetailValue, { children: trade.setup.primary_setup || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Secondary Setup' }),
                    _jsx(DetailValue, { children: trade.setup.secondary_setup || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Liquidity Taken' }),
                    _jsx(DetailValue, { children: trade.setup.liquidity_taken || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Pattern Quality' }),
                    _jsx(DetailValue, { children: trade.trade.pattern_quality_rating || 'N/A' }),
                  ],
                }),
              ],
            }),
          ],
        }),
      trade.analysis &&
        _jsxs(ExpandedSection, {
          children: [
            _jsx(SectionTitle, { children: 'Analysis' }),
            _jsxs(DetailGrid, {
              children: [
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Execution Quality' }),
                    _jsx(DetailValue, { children: trade.analysis?.execution_quality || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Lessons Learned' }),
                    _jsx(DetailValue, { children: trade.analysis?.lessons_learned || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Emotional State' }),
                    _jsx(DetailValue, { children: trade.analysis?.emotional_state || 'N/A' }),
                  ],
                }),
                _jsxs(DetailItem, {
                  children: [
                    _jsx(DetailLabel, { children: 'Market Conditions' }),
                    _jsx(DetailValue, { children: trade.analysis?.market_conditions || 'N/A' }),
                  ],
                }),
              ],
            }),
          ],
        }),
      trade.trade.notes &&
        _jsxs(ExpandedSection, {
          children: [
            _jsx(SectionTitle, { children: 'Notes' }),
            _jsx(DetailItem, { children: _jsx(DetailValue, { children: trade.trade.notes }) }),
          ],
        }),
      _jsx(ActionButtons, {
        children: _jsx(ActionButton, {
          to: `/trade-journal/edit/${trade.trade.id}`,
          children: 'Edit Trade',
        }),
      }),
    ],
  });
};
export default TradeListExpandedRow;
//# sourceMappingURL=TradeListExpandedRow.js.map
