import { jsx as _jsx } from 'react/jsx-runtime';
/**
 * Trade List Row Component
 *
 * Displays a row in the trade list
 */
import React from 'react';
import styled from 'styled-components';
const TradeItem = styled.div`
  display: grid;
  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: all ${({ theme }) => theme.transitions.fast};
  cursor: ${({ expanded }) => (expanded !== undefined ? 'pointer' : 'default')};
  position: relative;

  &:hover {
    background-color: ${({ theme }) => theme.colors.chartGrid};
  }
`;
/**
 * Trade List Row Component
 */
const TradeListRow = ({ trade, visibleColumns, expanded, toggleRowExpansion }) => {
  return _jsx(TradeItem, {
    expanded: expanded,
    onClick: () => toggleRowExpansion(trade.trade.id),
    children: visibleColumns.map(column =>
      _jsx(React.Fragment, { children: column.accessor(trade) }, column.id)
    ),
  });
};
export default TradeListRow;
//# sourceMappingURL=TradeListRow.js.map
