import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from 'react/jsx-runtime';
import styled, { css } from 'styled-components';
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';
const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  position: relative;
  overflow: hidden;

  /* F1 racing accent line */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%,
      transparent 100%
    );
  }

  /* Responsive design */
  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing?.md || '12px'};
    text-align: center;
  }
`;
const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const MainTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '24px'};
  font-weight: ${({ theme }) => theme.fontWeights?.bold || '700'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;

  span {
    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;
const LiveIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  color: ${({ theme, $isLive }) =>
    $isLive
      ? theme.colors?.primary || 'var(--primary-color)'
      : theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-weight: ${({ theme }) => theme.fontWeights?.bold || '700'};
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};

  &::before {
    content: '●';
    animation: ${({ $isLive }) => ($isLive ? 'f1-pulse 2s infinite' : 'none')};
  }

  @keyframes f1-pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;
const SessionInfo = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
`;
const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const RefreshButton = styled.button`
  background-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  padding: ${({ theme }) => `${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'}`};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};
  min-width: 100px;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
    cursor: not-allowed;
    transform: none;
  }

  ${({ $isRefreshing }) =>
    $isRefreshing &&
    css`
      pointer-events: none;
    `}
`;
const RefreshIcon = styled.span`
  display: inline-block;
  transition: transform 0.3s ease;

  ${({ $isRefreshing }) =>
    $isRefreshing &&
    css`
      animation: spin 1s linear infinite;
    `}

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;
/**
 * F1Header Component
 *
 * A reusable F1 racing-themed header component with live session indicator
 * and refresh functionality. Extracted from TradingDashboard for reusability.
 *
 * @example
 * ```typescript
 * <F1Header
 *   isLive={true}
 *   sessionName="London Session"
 *   onRefresh={handleRefresh}
 *   isRefreshing={isLoading}
 * />
 * ```
 */
export const F1Header = ({
  isLive = false,
  sessionName = 'Trading Session',
  onRefresh,
  isRefreshing = false,
  className,
  actions,
}) => {
  return _jsxs(HeaderContainer, {
    className: className,
    children: [
      _jsxs(TitleSection, {
        children: [
          _jsxs(MainTitle, {
            children: [
              '\uD83C\uDFCE\uFE0F TRADING ',
              _jsx('span', { children: '2025' }),
              ' DASHBOARD',
            ],
          }),
          _jsx(LiveIndicator, {
            $isLive: isLive,
            children: isLive ? 'LIVE SESSION' : 'SESSION CLOSED',
          }),
          sessionName && _jsx(SessionInfo, { children: sessionName }),
        ],
      }),
      _jsxs(ActionsSection, {
        children: [
          actions,
          onRefresh &&
            _jsx(RefreshButton, {
              onClick: onRefresh,
              disabled: isRefreshing,
              $isRefreshing: isRefreshing,
              'aria-label': isRefreshing ? 'Refreshing data' : 'Refresh data',
              children: isRefreshing
                ? _jsxs(_Fragment, {
                    children: [
                      _jsx(LoadingSpinner, { size: 'xs', variant: 'white' }),
                      'Refreshing...',
                    ],
                  })
                : _jsxs(_Fragment, {
                    children: [
                      _jsx(RefreshIcon, { $isRefreshing: false, children: '\uD83D\uDD04' }),
                      'Refresh Data',
                    ],
                  }),
            }),
        ],
      }),
    ],
  });
};
export default F1Header;
//# sourceMappingURL=F1Header.js.map
