import { jsx as _jsx } from 'react/jsx-runtime';
import { QuickTradeFormContainer } from './QuickTradeFormContainer';
/**
 * QuickTradeForm Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export const QuickTradeForm = props => {
  return _jsx(QuickTradeFormContainer, { ...props });
};
export default QuickTradeForm;
//# sourceMappingURL=QuickTradeForm.js.map
