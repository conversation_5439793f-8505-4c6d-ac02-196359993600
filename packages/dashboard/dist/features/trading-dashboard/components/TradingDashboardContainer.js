import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * TradingDashboardContainer Component
 *
 * FINAL CONTAINER: Main orchestrator for the refactored trading dashboard
 * Provides error boundaries, layout management, and component composition.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Comprehensive error boundaries
 * - Responsive layout management
 * - Feature flag support for gradual migration
 * - Performance optimized composition
 */
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';
import React, { Suspense } from 'react';
import styled from 'styled-components';
import { TradingDashboardProvider, useTradingDashboardDataFromContext, useTradingDashboardSession, useTradingDashboardTabs, } from '../context/TradingDashboardContext';
import { DashboardTabs, F1Header } from './index';
import { QuickTradeForm } from './QuickTradeForm';
// Import existing dashboard components
import MetricsPanel from '../components/MetricsPanel';
import PerformanceChart from '../components/PerformanceChart';
import RecentTradesTable from '../components/RecentTradesTable';
import SetupAnalysis from '../components/SetupAnalysis';
const DashboardLayout = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1400px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  min-height: 100vh;
`;
const ContentArea = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const TabContentContainer = styled.div `
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
const AnalyticsLayout = styled.div `
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${({ theme }) => theme.spacing?.xl || '24px'};

  @media (max-width: ${({ theme }) => theme.breakpoints?.lg || '1024px'}) {
    grid-template-columns: 1fr;
  }
`;
const ChartsSection = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const ErrorFallback = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  text-align: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const RetryButton = styled.button `
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '16px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;
/**
 * Error Boundary Component
 */
class DashboardErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }
    componentDidCatch(error, errorInfo) {
        console.error('TradingDashboard Error:', error, errorInfo);
    }
    retry = () => {
        this.setState({ hasError: false, error: null });
    };
    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback;
            if (FallbackComponent && this.state.error) {
                return _jsx(FallbackComponent, { error: this.state.error, retry: this.retry });
            }
            return (_jsxs(ErrorFallback, { children: [_jsx("div", { children: "\uD83D\uDEA8 Dashboard Error" }), _jsx("div", { children: "Something went wrong with the trading dashboard." }), _jsx("div", { style: { fontSize: '12px', opacity: 0.8 }, children: this.state.error?.message || 'Unknown error occurred' }), _jsx(RetryButton, { onClick: this.retry, children: "Retry Dashboard" })] }));
        }
        return this.props.children;
    }
}
/**
 * Loading Fallback Component
 */
const LoadingFallback = () => (_jsxs("div", { style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '400px',
        gap: '16px',
    }, children: [_jsx(LoadingSpinner, { size: 'lg' }), _jsx("div", { style: { color: 'var(--text-secondary)' }, children: "Loading Trading Dashboard..." })] }));
/**
 * Dashboard Content Component (uses context)
 */
const DashboardContent = () => {
    const { activeTab, setActiveTab } = useTradingDashboardTabs();
    const { isLive, sessionName } = useTradingDashboardSession();
    const { trades, performanceMetrics, chartData, setupPerformance, sessionPerformance, refreshData, isLoading, error, } = useTradingDashboardDataFromContext();
    const handleTradeSubmit = async (tradeData) => {
        console.log('Quick trade submitted:', tradeData);
        // In a real implementation, this would save the trade and refresh data
        await refreshData();
    };
    const renderTabContent = () => {
        console.log('🔄 TradingDashboardContainer renderTabContent called with activeTab:', activeTab);
        console.log('📊 Available data:', {
            tradesCount: trades?.length || 0,
            isLoading,
            error,
            performanceMetricsCount: performanceMetrics?.length || 0,
        });
        switch (activeTab) {
            case 'summary':
                // DEBUG: Log the trades data being passed to RecentTradesTable
                const recentTrades = trades.slice(0, 5);
                console.log('🎯 TradingDashboardContainer passing recent trades to RecentTradesTable:', recentTrades.map(t => ({
                    id: t.trade.id,
                    date: t.trade.date,
                    market: t.trade.market,
                    direction: t.trade.direction,
                })));
                console.log('🚀 About to render RecentTradesTable with', recentTrades.length, 'trades');
                return (_jsxs(TabContentContainer, { children: [_jsx(MetricsPanel, { metrics: performanceMetrics, isLoading: isLoading }), _jsx(PerformanceChart, { data: chartData, isLoading: isLoading }), _jsx(RecentTradesTable, { trades: recentTrades, isLoading: isLoading })] }));
            case 'trades':
                return (_jsx(TabContentContainer, { children: _jsx(RecentTradesTable, { trades: trades, isLoading: isLoading }) }));
            case 'setups':
                return (_jsx(TabContentContainer, { children: _jsx(SetupAnalysis, { setupPerformance: setupPerformance, sessionPerformance: sessionPerformance, isLoading: isLoading }) }));
            case 'analytics':
                return (_jsx(TabContentContainer, { children: _jsxs(AnalyticsLayout, { children: [_jsxs(ChartsSection, { children: [_jsx(MetricsPanel, { metrics: performanceMetrics, isLoading: isLoading }), _jsx(PerformanceChart, { data: chartData, isLoading: isLoading }), _jsx(SetupAnalysis, { setupPerformance: setupPerformance, sessionPerformance: sessionPerformance, isLoading: isLoading })] }), _jsx(QuickTradeForm, { onSubmit: handleTradeSubmit })] }) }));
            default:
                return (_jsx(TabContentContainer, { children: _jsxs("div", { style: { textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }, children: ["Unknown tab: ", activeTab] }) }));
        }
    };
    if (error) {
        return (_jsxs(ErrorFallback, { children: [_jsx("div", { children: "\u274C Data Loading Error" }), _jsx("div", { children: error }), _jsx(RetryButton, { onClick: refreshData, children: "Retry" })] }));
    }
    return (_jsxs(DashboardLayout, { children: [_jsx(F1Header, { isLive: isLive, sessionName: sessionName, onRefresh: refreshData, isRefreshing: isLoading }), _jsx(DashboardTabs, { activeTab: activeTab, onTabChange: setActiveTab, persistState: true, syncWithUrl: false }), _jsx(ContentArea, { children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: renderTabContent() }) })] }));
};
/**
 * TradingDashboardContainer Component
 *
 * Main container that provides context, error boundaries, and layout management
 * for the refactored trading dashboard. This is the final orchestrator component.
 */
export const TradingDashboardContainer = ({ className, _enableFeatureFlags: _unused = false, // Renamed to indicate unused parameter
initialTab = 'summary', }) => {
    return (_jsx("div", { className: className, children: _jsx(DashboardErrorBoundary, { children: _jsx(TradingDashboardProvider, { initialState: { activeTab: initialTab }, children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(DashboardContent, {}) }) }) }) }));
};
export default TradingDashboardContainer;
//# sourceMappingURL=TradingDashboardContainer.js.map