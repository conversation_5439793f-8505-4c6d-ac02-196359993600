import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useMemo, useState } from 'react';
import { useTradingDashboardData as useDataHook } from '../hooks/useTradingDashboardData';
const TradingDashboardContext = createContext(null);
/**
 * TradingDashboardProvider Component
 *
 * Provides centralized state management for the trading dashboard.
 * Uses performance optimization techniques to prevent unnecessary re-renders.
 */
export const TradingDashboardProvider = ({ children, initialState = {},
// dataFetcher, // Removed as unused
 }) => {
    // ✅ REAL DATA INTEGRATION: Using the refactored data hook
    const { trades, performanceMetrics, chartData, setupPerformance, sessionPerformance, lastUpdated, isLoading, error, refreshData: refreshDataHook, clearError: clearErrorHook, } = useDataHook();
    // UI state management (separate from data state)
    const [activeTab, setActiveTab] = useState(initialState.activeTab || 'summary');
    // Session management utilities
    const sessionUtils = useMemo(() => ({
        isLiveSession: () => {
            const now = new Date();
            const hour = now.getHours();
            // Market hours logic: 9 AM - 4 PM EST (adjust for your timezone)
            return hour >= 9 && hour < 16;
        },
        getCurrentSessionName: () => {
            const now = new Date();
            const hour = now.getHours();
            if (hour >= 2 && hour < 8)
                return 'Sydney Session';
            if (hour >= 8 && hour < 14)
                return 'London Session';
            if (hour >= 14 && hour < 20)
                return 'New York Session';
            return 'After Hours';
        },
    }), []);
    // Actions with real implementations
    const actions = {
        setActiveTab: (tab) => {
            setActiveTab(tab);
        },
        refreshData: async () => {
            await refreshDataHook();
        },
        clearError: () => {
            clearErrorHook();
        },
        isLiveSession: sessionUtils.isLiveSession,
        getCurrentSessionName: sessionUtils.getCurrentSessionName,
    };
    // Memoize context value to prevent unnecessary re-renders
    const contextValue = useMemo(() => ({
        // Data state from hook
        trades,
        performanceMetrics,
        chartData,
        setupPerformance,
        sessionPerformance,
        lastUpdated,
        isLoading,
        error,
        // UI state
        activeTab,
        // Actions
        ...actions,
    }), [
        // Dependencies for memoization (only include what actually changes)
        trades,
        performanceMetrics,
        chartData,
        setupPerformance,
        sessionPerformance,
        lastUpdated,
        isLoading,
        error,
        activeTab,
        actions,
    ]);
    return (_jsx(TradingDashboardContext.Provider, { value: contextValue, children: children }));
};
/**
 * Hook to use TradingDashboardContext
 *
 * @throws Error if used outside of TradingDashboardProvider
 * @returns TradingDashboardContextValue
 *
 * @example
 * ```typescript
 * const { trades, activeTab, setActiveTab, refreshData } = useTradingDashboardContext();
 * ```
 */
export const useTradingDashboardContext = () => {
    const context = useContext(TradingDashboardContext);
    if (!context) {
        throw new Error('useTradingDashboardContext must be used within a TradingDashboardProvider. ' +
            'Make sure to wrap your component with <TradingDashboardProvider>.');
    }
    return context;
};
/**
 * Convenience hooks for specific parts of the context
 */
/**
 * Hook for tab management
 */
export const useTradingDashboardTabs = () => {
    const { activeTab, setActiveTab } = useTradingDashboardContext();
    return { activeTab, setActiveTab };
};
/**
 * Hook for data management (context-based)
 */
export const useTradingDashboardDataFromContext = () => {
    const { trades, performanceMetrics, chartData, setupPerformance, sessionPerformance, isLoading, error, lastUpdated, refreshData, clearError, } = useTradingDashboardContext();
    return {
        trades,
        performanceMetrics,
        chartData,
        setupPerformance,
        sessionPerformance,
        isLoading,
        error,
        lastUpdated,
        refreshData,
        clearError,
    };
};
/**
 * Hook for session management
 */
export const useTradingDashboardSession = () => {
    const { isLiveSession, getCurrentSessionName } = useTradingDashboardContext();
    return {
        isLive: isLiveSession(),
        sessionName: getCurrentSessionName(),
    };
};
export default TradingDashboardContext;
//# sourceMappingURL=TradingDashboardContext.js.map