{"version": 3, "file": "useQuickTradeForm.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/hooks/useQuickTradeForm.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AAEH,OAAO,EAEL,YAAY,EACZ,eAAe,EACf,eAAe,GAChB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAoCzD;;GAEG;AACH,MAAM,oBAAoB,GAAG,GAAkB,EAAE,CAAC,CAAC;IACjD,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,EAAE,KAAK;IACb,SAAS,EAAE,MAAM;IACjB,QAAQ,EAAE,GAAG;IACb,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,EAAE;IACT,OAAO,EAAE,EAAE;IACX,KAAK,EAAE,EAAE;IACT,cAAc,EAAE,EAAE;IAClB,SAAS,EAAE,EAAE;IACb,MAAM,EAAE,EAAE;IACV,eAAe,EAAE,EAAE;IACnB,YAAY,EAAE,EAAE;IAChB,KAAK,EAAE,EAAE;IACT,IAAI,EAAE,EAAE;IACR,MAAM,EAAE,KAAK;CACd,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAC/B,SAAkC,EAAE,EACX,EAAE;IAC3B,MAAM,EAAE,QAAQ,EAAE,aAAa,GAAG,EAAE,EAAE,QAAQ,GAAG,IAAI,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;IAE3F,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,eAAe,EAAE,CAAC;IACnE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAC5D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAc,IAAI,CAAC,CAAC;IAE9D,8BAA8B;IAC9B,MAAM,SAAS,GAAG,YAAY,CAAC;QAC7B,YAAY,EAAE,aAAa,CAAC,IAAI,IAAI,oBAAoB,EAAE,CAAC,IAAI;QAC/D,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;KAChE,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,YAAY,CAAC;QAC/B,YAAY,EAAE,aAAa,CAAC,MAAM,IAAI,oBAAoB,EAAE,CAAC,MAAM;QACnE,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;KAClE,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,YAAY,CAAC;QAClC,YAAY,EAAE,aAAa,CAAC,SAAS,IAAI,oBAAoB,EAAE,CAAC,SAAS;QACzE,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KACrE,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,YAAY,CAAC;QACjC,YAAY,EAAE,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;QACvF,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE;YACf,eAAe,CAAC,QAAQ,CAAC,sBAAsB,CAAC;YAChD,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,6BAA6B,CAAC;SACtD;KACF,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,YAAY,CAAC;QACnC,YAAY,EAAE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC,UAAU,CAAC;QAC3F,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE;YACf,eAAe,CAAC,QAAQ,CAAC,yBAAyB,CAAC;YACnD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,oCAAoC,CAAC;SAChE;KACF,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,YAAY,CAAC;QAClC,YAAY,EAAE,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC,SAAS,CAAC;QACzF,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE;YACf,eAAe,CAAC,QAAQ,CAAC,wBAAwB,CAAC;YAClD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,mCAAmC,CAAC;SAC/D;KACF,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,aAAa,GAAG,WAAW,CAC/B,GAAkB,EAAE,CAAC,CAAC;QACpB,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;QAC7B,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;QACjC,SAAS,EAAE,cAAc,CAAC,KAAyB;QACnD,QAAQ,EAAE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC;QACrC,UAAU,EAAE,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC;QACzC,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;QACvC,MAAM,EAAE,GAAG,EAAE,2BAA2B;QACxC,KAAK,EAAE,EAAE;QACT,OAAO,EAAE,EAAE;QACX,KAAK,EAAE,EAAE;QACT,cAAc,EAAE,EAAE;QAClB,SAAS,EAAE,EAAE;QACb,MAAM,EAAE,EAAE;QACV,eAAe,EAAE,EAAE;QACnB,YAAY,EAAE,EAAE;QAChB,KAAK,EAAE,EAAE;QACT,IAAI,EAAE,EAAE;QACR,MAAM,EAAE,KAAK;KACd,CAAC,EACF;QACE,SAAS,CAAC,KAAK;QACf,WAAW,CAAC,KAAK;QACjB,cAAc,CAAC,KAAK;QACpB,aAAa,CAAC,KAAK;QACnB,eAAe,CAAC,KAAK;QACrB,cAAc,CAAC,KAAK;KACrB,CACF,CAAC;IAEF;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAAC,GAAY,EAAE;QAC7C,OAAO,CACL,SAAS,CAAC,KAAK;YACf,WAAW,CAAC,KAAK;YACjB,cAAc,CAAC,KAAK;YACpB,aAAa,CAAC,KAAK;YACnB,eAAe,CAAC,KAAK;YACrB,cAAc,CAAC,KAAK,CACrB,CAAC;IACJ,CAAC,EAAE;QACD,SAAS,CAAC,KAAK;QACf,WAAW,CAAC,KAAK;QACjB,cAAc,CAAC,KAAK;QACpB,aAAa,CAAC,KAAK;QACnB,eAAe,CAAC,KAAK;QACrB,cAAc,CAAC,KAAK;KACrB,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC1C,0BAA0B;QAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,sBAAsB;QACtB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAChC,SAAS,CAAC,QAAQ,EAAE;YACpB,WAAW,CAAC,QAAQ,EAAE;YACtB,cAAc,CAAC,QAAQ,EAAE;YACzB,aAAa,CAAC,QAAQ,EAAE;YACxB,eAAe,CAAC,QAAQ,EAAE;YAC1B,cAAc,CAAC,QAAQ,EAAE;SAC1B,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,QAAQ,CAAC,gDAAgD,CAAC,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,MAAM,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3B,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;gBAEnC,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,2BAA2B;oBAC3B,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;oBAC7C,oBAAoB;oBACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC1D,CAAC;gBAED,UAAU,CAAC,+BAA+B,CAAC,CAAC;gBAE5C,yCAAyC;gBACzC,UAAU,CAAC,GAAG,EAAE;oBACd,WAAW,EAAE,CAAC;oBACd,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC,EAAE,IAAI,CAAC,CAAC;YACX,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAChD,QAAQ,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE;QACD,SAAS;QACT,WAAW;QACX,cAAc;QACd,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,QAAQ;QACR,WAAW;KACZ,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;QACnC,MAAM,QAAQ,GAAG,oBAAoB,EAAE,CAAC;QACxC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACtC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC5C,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClD,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;QACtD,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,UAAU,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC;IAE7F;;OAEG;IACH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;YAEnC,6CAA6C;YAC7C,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/D,qBAAqB;gBACrB,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC;gBAClD,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAErB,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC;IAEhD,OAAO;QACL,cAAc;QACd,SAAS;QACT,WAAW;QACX,cAAc;QACd,aAAa;QACb,eAAe;QACf,cAAc;QAEd,aAAa;QACb,YAAY;QACZ,KAAK;QACL,OAAO;QAEP,eAAe;QACf,YAAY;QACZ,WAAW;QACX,YAAY;QAEZ,YAAY;QACZ,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,iBAAiB,CAAC"}