import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { Card } from '@adhd-trading-dashboard/shared';
/**
 * Trade Analysis Summary Component
 *
 * A component for displaying trade analysis summary metrics.
 */
export const TradeAnalysisSummary = ({ summary, isLoading = false }) => {
  // Format currency
  const formatCurrency = value => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };
  // Format percentage
  const formatPercentage = value => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  // Format number
  const formatNumber = value => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  // Metric card style
  const metricCardStyle = {
    padding: '16px',
    borderRadius: '8px',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  };
  // Metric value style
  const metricValueStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '4px',
  };
  // Metric label style
  const metricLabelStyle = {
    fontSize: '14px',
    color: '#666',
  };
  // Loading state
  if (isLoading) {
    return _jsx(Card, {
      title: 'Performance Summary',
      children: _jsx('div', {
        style: { padding: '24px', textAlign: 'center' },
        children: 'Loading summary data...',
      }),
    });
  }
  return _jsx(Card, {
    title: 'Performance Summary',
    children: _jsxs('div', {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
        gap: '16px',
        padding: '16px',
      },
      children: [
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: summary.totalTrades }),
            _jsx('div', { style: metricLabelStyle, children: 'Total Trades' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: formatPercentage(summary.winRate) }),
            _jsx('div', { style: metricLabelStyle, children: 'Win Rate' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: formatNumber(summary.profitFactor) }),
            _jsx('div', { style: metricLabelStyle, children: 'Profit Factor' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: formatCurrency(summary.netProfit) }),
            _jsx('div', { style: metricLabelStyle, children: 'Net Profit' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: formatCurrency(summary.averageWin) }),
            _jsx('div', { style: metricLabelStyle, children: 'Average Win' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: formatCurrency(summary.averageLoss) }),
            _jsx('div', { style: metricLabelStyle, children: 'Average Loss' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: summary.winningTrades }),
            _jsx('div', { style: metricLabelStyle, children: 'Winning Trades' }),
          ],
        }),
        _jsxs('div', {
          style: metricCardStyle,
          children: [
            _jsx('div', { style: metricValueStyle, children: summary.losingTrades }),
            _jsx('div', { style: metricLabelStyle, children: 'Losing Trades' }),
          ],
        }),
      ],
    }),
  });
};
//# sourceMappingURL=TradeAnalysisSummary.js.map
