import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Enhanced Filter Panel Component
 *
 * Modern, structured filtering interface for trade analysis
 * with improved visual hierarchy and responsive design.
 */
import { useState } from 'react';
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { Tag } from '@adhd-trading-dashboard/shared';
// Main container with F1-inspired design
const Container = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
`;
// Header section with title and actions
const FilterHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--bg-primary) 0%, #2a2a2a 100%);
  border-bottom: 1px solid var(--border-primary);
`;
const HeaderTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;
const Title = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
`;
const LiveIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--error-color);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--error-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;
const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;
// Filter content area
const FilterContent = styled.div`
  padding: 24px;
`;
const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
`;
const FilterGroup = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 16px;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: var(--text-secondary);
  }
`;
const FilterLabel = styled.div`
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '';
    width: 3px;
    height: 12px;
    background: var(--error-color);
    border-radius: 2px;
  }
`;
const DateRangeContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;
const DateInput = styled.input`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  padding: 10px 12px;
  color: #ffffff;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
  }

  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
  }
`;
const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;
const FilterTag = styled(Tag)`
  cursor: pointer;
  opacity: ${({ selected }) => (selected ? 1 : 0.6)};
  background: ${({ selected, variant }) => {
    if (!selected) return 'var(--border-primary)';
    switch (variant) {
      case 'success':
        return 'var(--success-color)';
      case 'error':
        return 'var(--error-color)';
      case 'info':
        return 'var(--info-color)';
      case 'primary':
        return '#8b5cf6';
      case 'secondary':
        return 'var(--warning-color)';
      default:
        return 'var(--text-secondary)';
    }
  }};
  color: ${({ selected }) => (selected ? '#ffffff' : 'var(--text-secondary)')};
  border: 1px solid
    ${({ selected, variant }) => {
      if (!selected) return 'var(--border-primary)';
      switch (variant) {
        case 'success':
          return 'var(--success-color)';
        case 'error':
          return 'var(--error-color)';
        case 'info':
          return 'var(--info-color)';
        case 'primary':
          return '#8b5cf6';
        case 'secondary':
          return 'var(--warning-color)';
        default:
          return 'var(--text-secondary)';
      }
    }};
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.8;
    transform: translateY(-1px);
  }
`;
const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-primary);
`;
const FilterStats = styled.div`
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
`;
const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
`;
const ActionButton = styled.button`
  background: ${({ variant }) => (variant === 'primary' ? 'var(--error-color)' : 'transparent')};
  color: ${({ variant }) => (variant === 'primary' ? '#ffffff' : 'var(--text-secondary)')};
  border: 1px solid
    ${({ variant }) => (variant === 'primary' ? 'var(--error-color)' : 'var(--border-primary)')};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;

  &:hover {
    background: ${({ variant }) =>
      variant === 'primary' ? 'var(--primary-color)' : 'var(--border-primary)'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;
export const FilterPanel = ({ className }) => {
  const { filters, updateFilters, resetFilters, data } = useTradeAnalysis();
  // Local state for filter values
  const [localFilters, setLocalFilters] = useState(filters);
  // Available options from data
  const availableSymbols = data?.trades ? [...new Set(data.trades.map(trade => trade.symbol))] : [];
  const availableStrategies = data?.trades
    ? [...new Set(data.trades.map(trade => trade.strategy))]
    : [];
  const availableTags = data?.trades
    ? [...new Set(data.trades.flatMap(trade => trade.tags || []))]
    : [];
  // Direction options
  const directionOptions = ['long', 'short'];
  // Status options
  const statusOptions = ['win', 'loss', 'breakeven'];
  // Timeframe options
  const timeframeOptions = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];
  // Session options
  const sessionOptions = ['pre-market', 'regular', 'after-hours'];
  // Calculate filter stats
  const activeFilterCount = Object.values(localFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) return Object.values(value).some(v => v);
    return value !== undefined && value !== null && value !== '';
  }).length;
  const totalTrades = data?.trades?.length || 0;
  // Handle date range change
  const handleDateChange = (field, value) => {
    setLocalFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: value,
      },
    }));
  };
  // Handle array filter toggle
  const handleToggleFilter = (field, value) => {
    setLocalFilters(prev => {
      const currentValues = prev[field] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return {
        ...prev,
        [field]: newValues.length > 0 ? newValues : undefined,
      };
    });
  };
  // Apply filters
  const applyFilters = () => {
    updateFilters(localFilters);
  };
  // Reset filters
  const handleResetFilters = () => {
    resetFilters();
    setLocalFilters(filters);
  };
  // Check if a filter value is selected
  const isSelected = (field, value) => {
    const values = localFilters[field];
    return values ? values.includes(value) : false;
  };
  return _jsxs(Container, {
    className: className,
    children: [
      _jsxs(FilterHeader, {
        children: [
          _jsxs(HeaderTitle, {
            children: [
              _jsx(Title, { children: 'Trade Analysis' }),
              _jsx(LiveIndicator, { children: 'LIVE' }),
            ],
          }),
          _jsx(HeaderActions, {
            children: _jsx(ActionButton, {
              variant: 'secondary',
              onClick: handleResetFilters,
              children: 'Reset',
            }),
          }),
        ],
      }),
      _jsx(FilterContent, {
        children: _jsxs(FilterGrid, {
          children: [
            _jsxs(FilterGroup, {
              children: [
                _jsx(FilterLabel, { children: 'Date Range' }),
                _jsxs(DateRangeContainer, {
                  children: [
                    _jsx(DateInput, {
                      type: 'date',
                      value: localFilters.dateRange.startDate,
                      onChange: e => handleDateChange('startDate', e.target.value),
                      placeholder: 'Start Date',
                    }),
                    _jsx(DateInput, {
                      type: 'date',
                      value: localFilters.dateRange.endDate,
                      onChange: e => handleDateChange('endDate', e.target.value),
                      placeholder: 'End Date',
                    }),
                  ],
                }),
              ],
            }),
            _jsxs(FilterGroup, {
              children: [
                _jsx(FilterLabel, { children: 'Direction' }),
                _jsx(TagsContainer, {
                  children: directionOptions.map(direction =>
                    _jsx(
                      FilterTag,
                      {
                        variant: direction === 'long' ? 'success' : 'error',
                        selected: isSelected('directions', direction),
                        onClick: () => handleToggleFilter('directions', direction),
                        children: direction,
                      },
                      direction
                    )
                  ),
                }),
              ],
            }),
            _jsxs(FilterGroup, {
              children: [
                _jsx(FilterLabel, { children: 'Status' }),
                _jsx(TagsContainer, {
                  children: statusOptions.map(status =>
                    _jsx(
                      FilterTag,
                      {
                        variant:
                          status === 'win' ? 'success' : status === 'loss' ? 'error' : 'info',
                        selected: isSelected('statuses', status),
                        onClick: () => handleToggleFilter('statuses', status),
                        children: status,
                      },
                      status
                    )
                  ),
                }),
              ],
            }),
            availableSymbols.length > 0 &&
              _jsxs(FilterGroup, {
                children: [
                  _jsx(FilterLabel, { children: 'Symbols' }),
                  _jsx(TagsContainer, {
                    children: availableSymbols.map(symbol =>
                      _jsx(
                        FilterTag,
                        {
                          variant: 'primary',
                          selected: isSelected('symbols', symbol),
                          onClick: () => handleToggleFilter('symbols', symbol),
                          children: symbol,
                        },
                        symbol
                      )
                    ),
                  }),
                ],
              }),
            availableStrategies.length > 0 &&
              _jsxs(FilterGroup, {
                children: [
                  _jsx(FilterLabel, { children: 'Strategies' }),
                  _jsx(TagsContainer, {
                    children: availableStrategies.map(strategy =>
                      _jsx(
                        FilterTag,
                        {
                          variant: 'secondary',
                          selected: isSelected('strategies', strategy),
                          onClick: () => handleToggleFilter('strategies', strategy),
                          children: strategy,
                        },
                        strategy
                      )
                    ),
                  }),
                ],
              }),
            _jsxs(FilterGroup, {
              children: [
                _jsx(FilterLabel, { children: 'Timeframe' }),
                _jsx(TagsContainer, {
                  children: timeframeOptions.map(timeframe =>
                    _jsx(
                      FilterTag,
                      {
                        variant: 'default',
                        selected: isSelected('timeframes', timeframe),
                        onClick: () => handleToggleFilter('timeframes', timeframe),
                        children: timeframe,
                      },
                      timeframe
                    )
                  ),
                }),
              ],
            }),
            _jsxs(FilterGroup, {
              children: [
                _jsx(FilterLabel, { children: 'Session' }),
                _jsx(TagsContainer, {
                  children: sessionOptions.map(session =>
                    _jsx(
                      FilterTag,
                      {
                        variant: 'default',
                        selected: isSelected('sessions', session),
                        onClick: () => handleToggleFilter('sessions', session),
                        children: session,
                      },
                      session
                    )
                  ),
                }),
              ],
            }),
            availableTags.length > 0 &&
              _jsxs(FilterGroup, {
                children: [
                  _jsx(FilterLabel, { children: 'Tags' }),
                  _jsx(TagsContainer, {
                    children: availableTags.map(tag =>
                      _jsx(
                        FilterTag,
                        {
                          variant: 'info',
                          selected: isSelected('tags', tag),
                          onClick: () => handleToggleFilter('tags', tag),
                          children: tag,
                        },
                        tag
                      )
                    ),
                  }),
                ],
              }),
          ],
        }),
      }),
      _jsxs(ActionBar, {
        children: [
          _jsx(FilterStats, {
            children:
              activeFilterCount > 0
                ? `${activeFilterCount} filter${
                    activeFilterCount > 1 ? 's' : ''
                  } active • ${totalTrades} trades`
                : `${totalTrades} trades • No filters applied`,
          }),
          _jsx(ActionButtons, {
            children: _jsx(ActionButton, {
              variant: 'primary',
              onClick: applyFilters,
              children: 'Apply Filters',
            }),
          }),
        ],
      }),
    ],
  });
};
//# sourceMappingURL=FilterPanel.js.map
