/**
 * Real Trade Analysis API Service
 *
 * This service replaces the mock data generation with real trade data integration
 * from the trade journal system using the trade storage service.
 */
import {
  tradeStorageService,
  // TradeFilters, // Removed as unused
} from '@adhd-trading-dashboard/shared';
import {
  calculateRealPerformanceMetrics,
  calculateCategoryPerformance,
  calculateTimePerformance,
  generateEquityCurve,
  generateDistributionData,
} from './tradeAnalysisCalculations';
import { batchCalculateAnalytics, performanceMonitor } from './performanceCache';
/**
 * Fetch real trade analysis data from the trade storage service
 */
export const fetchRealTradeAnalysisData = async filters => {
  try {
    console.log('🔄 Fetching real trade analysis data with filters:', filters);
    // Convert analysis filters to storage service filters (for future use)
    // const storageFilters = convertToStorageFilters(filters);
    // Fetch trades from IndexedDB
    const trades = await tradeStorageService.getAllTrades();
    console.log(`📊 Retrieved ${trades.length} trades from storage`);
    // Apply additional client-side filtering if needed
    const filteredTrades = applyClientSideFilters(trades, filters);
    console.log(`🔍 After client-side filtering: ${filteredTrades.length} trades`);
    // Calculate all metrics and performance data
    const analysisData = await generateAnalysisData(filteredTrades);
    console.log('✅ Trade analysis data generated successfully');
    return analysisData;
  } catch (error) {
    console.error('❌ Error fetching real trade analysis data:', error);
    throw new Error(
      `Failed to fetch trade analysis data: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
};
/**
 * Generate comprehensive analysis data from filtered trades
 */
const generateAnalysisData = async trades => {
  console.log('🧮 Starting performance analysis with caching...');
  // Log memory usage before calculation
  performanceMonitor.logMemoryUsage('before analysis');
  // Use batch calculation with caching for optimal performance
  const analyticsResults = await performanceMonitor.measureTime('Batch Analytics Calculation', () =>
    batchCalculateAnalytics(trades, {
      performanceMetrics: calculateRealPerformanceMetrics,
      categoryPerformance: calculateCategoryPerformance,
      timePerformance: calculateTimePerformance,
      equityCurve: generateEquityCurve,
      distributionData: generateDistributionData,
    })
  );
  // Extract results from batch calculation
  const {
    metrics,
    symbolPerformance,
    strategyPerformance,
    sessionPerformance,
    setupPerformance,
    directionPerformance,
    timeOfDayPerformance,
    dayOfWeekPerformance,
    monthlyPerformance,
    equityCurve,
    distributionData,
  } = analyticsResults;
  // Log memory usage after calculation
  performanceMonitor.logMemoryUsage('after analysis');
  // Log cache statistics
  const cacheStats = performanceMonitor.getCacheStats();
  console.log('📊 Cache statistics:', cacheStats);
  return {
    trades: trades.map(convertToAnalysisTradeFormat),
    metrics,
    symbolPerformance,
    strategyPerformance,
    timeframePerformance: [], // Not applicable with current schema
    sessionPerformance,
    setupPerformance,
    directionPerformance,
    timeOfDayPerformance,
    dayOfWeekPerformance,
    monthlyPerformance,
    equityCurve,
    distributionData,
    // Additional analysis data
    totalTrades: trades.length,
    dateRange: {
      start:
        trades.length > 0
          ? Math.min(...trades.map(t => new Date(t.trade.date).getTime()))
          : Date.now(),
      end:
        trades.length > 0
          ? Math.max(...trades.map(t => new Date(t.trade.date).getTime()))
          : Date.now(),
    },
    lastUpdated: new Date().toISOString(),
  };
};
/**
 * Convert analysis filters to trade storage service filters
 */
// convertToStorageFilters function removed as unused
/*
const convertToStorageFilters = (filters: AnalysisFilters): TradeFilters => {
  const storageFilters: TradeFilters = {};

  // Date range filters
  if (filters.dateRange) {
    storageFilters.dateFrom = filters.dateRange.startDate;
    storageFilters.dateTo = filters.dateRange.endDate;
  }

  // Symbol filter (maps to market in our schema)
  if (filters.symbols && filters.symbols.length > 0) {
    // Note: Storage service might need enhancement to support multiple symbols
    storageFilters.market = filters.symbols[0]; // For now, use first symbol
  }

  // Direction filter
  if (filters.directions && filters.directions.length > 0) {
    // Convert 'long'/'short' to 'Long'/'Short'
    const direction = filters.directions[0];
    storageFilters.direction = direction === 'long' ? 'Long' : 'Short';
  }

  // Session filter
  if (filters.sessions && filters.sessions.length > 0) {
    storageFilters.session = filters.sessions[0];
  }

  // Strategy filter (maps to model_type in our schema)
  if (filters.strategies && filters.strategies.length > 0) {
    storageFilters.model_type = filters.strategies[0];
  }

  // Profit/Loss filters - TradeFilters doesn't have these properties
  // TODO: Add profit/loss filtering support to TradeFilters interface
  // if (filters.minProfitLoss !== undefined) {
  //   storageFilters.min_achieved_pl = filters.minProfitLoss;
  // }
  // if (filters.maxProfitLoss !== undefined) {
  //   storageFilters.max_achieved_pl = filters.maxProfitLoss;
  // }

  return storageFilters;
};
*/
/**
 * Apply additional client-side filtering that can't be done at the database level
 */
const applyClientSideFilters = (trades, filters) => {
  return trades.filter(tradeData => {
    const trade = tradeData.trade;
    // Multiple symbols filter
    if (filters.symbols && filters.symbols.length > 1) {
      const hasMatchingSymbol = filters.symbols.some(symbol =>
        trade.market?.toLowerCase().includes(symbol.toLowerCase())
      );
      if (!hasMatchingSymbol) return false;
    }
    // Multiple directions filter
    if (filters.directions && filters.directions.length > 1) {
      const tradeDirection = trade.direction?.toLowerCase();
      const hasMatchingDirection = filters.directions.some(
        direction => direction === tradeDirection
      );
      if (!hasMatchingDirection) return false;
    }
    // Multiple sessions filter
    if (filters.sessions && filters.sessions.length > 1) {
      const tradeSession = trade.session || '';
      if (!filters.sessions.includes(tradeSession)) return false;
    }
    // Multiple strategies filter
    if (filters.strategies && filters.strategies.length > 1) {
      if (!filters.strategies.includes(trade.model_type || '')) return false;
    }
    // Status filter (win/loss/breakeven)
    if (filters.statuses && filters.statuses.length > 0) {
      const tradeStatus = getTradeStatus(trade.achieved_pl || 0);
      if (!filters.statuses.includes(tradeStatus)) return false;
    }
    // Tags filter (if we add tags support in the future)
    if (filters.tags && filters.tags.length > 0) {
      // TODO: Implement tags filtering when tags are added to schema
    }
    return true;
  });
};
/**
 * Convert CompleteTradeData to the format expected by analysis components
 */
const convertToAnalysisTradeFormat = tradeData => {
  const trade = tradeData.trade;
  return {
    id: trade.id?.toString() || `${trade.date}-${trade.market}`,
    symbol: trade.market || 'Unknown',
    direction: trade.direction?.toLowerCase() || 'unknown',
    entryPrice: trade.entry_price || 0,
    exitPrice: trade.exit_price || 0,
    quantity: trade.no_of_contracts || 0,
    entryTime: trade.entry_time || trade.date,
    exitTime: trade.exit_time || trade.date,
    status: getTradeStatus(trade.achieved_pl || 0),
    profitLoss: trade.achieved_pl || 0,
    profitLossPercent: calculateProfitLossPercent(trade),
    timeframe: '15m', // Default since not in current schema
    session: trade.session || 'regular',
    strategy: trade.model_type || 'Unknown',
    setup: trade.setup || 'Unknown',
    rMultiple: trade.r_multiple || 0,
    tags: [], // TODO: Add tags support
    notes: trade.notes || '',
    // Additional fields from our schema
    patternQuality: trade.pattern_quality_rating || 0,
    dolTarget: trade.dol_target || '',
    rdType: trade.rd_type || '',
    drawOnLiquidity: trade.draw_on_liquidity || '',
  };
};
/**
 * Determine trade status based on P&L
 */
const getTradeStatus = profitLoss => {
  if (profitLoss > 0) return 'win';
  if (profitLoss < 0) return 'loss';
  return 'breakeven';
};
/**
 * Calculate profit/loss percentage
 */
const calculateProfitLossPercent = trade => {
  if (!trade.entry_price || trade.entry_price === 0) return 0;
  const profitLoss = trade.achieved_pl || 0;
  const entryValue = trade.entry_price * (trade.no_of_contracts || 1);
  return entryValue > 0 ? (profitLoss / entryValue) * 100 : 0;
};
/**
 * Get available filter options from real trade data
 */
export const getFilterOptions = async () => {
  try {
    const trades = await tradeStorageService.getAllTrades();
    const symbols = [...new Set(trades.map(t => t.trade.market).filter(x => Boolean(x)))];
    const strategies = [...new Set(trades.map(t => t.trade.model_type).filter(x => Boolean(x)))];
    const sessions = [...new Set(trades.map(t => t.trade.session).filter(x => Boolean(x)))];
    const setups = [...new Set(trades.map(t => t.trade.setup).filter(x => Boolean(x)))];
    return {
      symbols: symbols.sort(),
      strategies: strategies.sort(),
      sessions: sessions.sort(),
      setups: setups.sort(),
    };
  } catch (error) {
    console.error('Error getting filter options:', error);
    return {
      symbols: [],
      strategies: [],
      sessions: [],
      setups: [],
    };
  }
};
/**
 * Get trade statistics for dashboard summary
 */
export const getTradeStatistics = async () => {
  try {
    const trades = await tradeStorageService.getAllTrades();
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const todayTrades = trades.filter(t => new Date(t.trade.date) >= today).length;
    const weekTrades = trades.filter(t => new Date(t.trade.date) >= weekAgo).length;
    const monthTrades = trades.filter(t => new Date(t.trade.date) >= monthAgo).length;
    const lastTradeDate =
      trades.length > 0 ? Math.max(...trades.map(t => new Date(t.trade.date).getTime())) : null;
    return {
      totalTrades: trades.length,
      todayTrades,
      weekTrades,
      monthTrades,
      lastTradeDate: lastTradeDate ? new Date(lastTradeDate).toISOString() : null,
    };
  } catch (error) {
    console.error('Error getting trade statistics:', error);
    return {
      totalTrades: 0,
      todayTrades: 0,
      weekTrades: 0,
      monthTrades: 0,
      lastTradeDate: null,
    };
  }
};
//# sourceMappingURL=realTradeAnalysisApi.js.map
