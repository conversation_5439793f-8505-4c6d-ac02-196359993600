/**
 * Dashboard Data Hook
 *
 * Custom hook for fetching and managing dashboard data
 */
import { useState, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
export const useDashboardData = () => {
  const [metrics] = useState([
    { title: 'Win Rate', value: '65%' },
    { title: 'Profit Factor', value: '2.3' },
    { title: 'Net Profit', value: '$12,500' },
    { title: 'Total Trades', value: '120' },
  ]);
  const [chartData, setChartData] = useState([]);
  const [recentTrades, setRecentTrades] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  // Mock data generation for demo purposes
  const generateChartData = () => {
    const data = [];
    const today = new Date();
    for (let i = 30; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        value: 10000 + Math.random() * 5000,
      });
    }
    return data;
  };
  // Mock trade data - FIXED: Now properly sorted newest first
  const generateTradeData = () => {
    const trades = [];
    const today = new Date();
    const markets = ['MNQ', 'NQ', 'ES', 'MES'];
    const sessions = ['NY Open', 'Lunch Macro', 'MOC'];
    const modelTypes = ['RD-Cont', 'FVG-RD', 'True-RD'];
    for (let i = 0; i < 5; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const isWin = Math.random() > 0.35;
      const profit = isWin ? Math.random() * 1000 : -Math.random() * 500;
      trades.push({
        trade: {
          id: i + 1,
          date: date.toISOString().split('T')[0],
          market: markets[Math.floor(Math.random() * markets.length)],
          direction: Math.random() > 0.5 ? 'Long' : 'Short',
          session: sessions[Math.floor(Math.random() * sessions.length)],
          model_type: modelTypes[Math.floor(Math.random() * modelTypes.length)],
          entry_price: 15000 + Math.random() * 1000,
          exit_price: 15000 + Math.random() * 1000,
          no_of_contracts: Math.floor(Math.random() * 5) + 1,
          achieved_pl: profit,
          r_multiple: profit / 100,
          win_loss: isWin ? 'Win' : 'Loss',
          pattern_quality_rating: Math.floor(Math.random() * 5) + 1,
          entry_time: '09:30',
          exit_time: '10:15',
          notes: `Mock trade ${i + 1}`,
        },
        fvg_details: undefined,
        setup: undefined,
        analysis: undefined,
      });
    }
    // FIXED: Sort trades by date descending (newest first) for proper "Recent Trades" display
    return trades.sort(
      (a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime()
    );
  };
  const fetchDashboardData = useCallback(async () => {
    console.log('🔄 fetchDashboardData called');
    setIsLoading(true);
    setError(null);
    try {
      // UPDATED: Fetch real data from IndexedDB instead of mock data
      console.log('📊 Fetching real data from IndexedDB...');
      const completeTradeData = await tradeStorageService.getAllTrades();
      console.log(`📈 Fetched ${completeTradeData.length} trades from IndexedDB`);
      if (completeTradeData.length > 0) {
        console.log(
          '📅 Raw trade dates (unsorted):',
          completeTradeData.map(t => ({ id: t.trade.id, date: t.trade.date }))
        );
        // Sort trades by date descending (newest first) and limit to 5 for recent trades
        const sortedTrades = [...completeTradeData].sort(
          (a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime()
        );
        console.log(
          '📅 Sorted trade dates (newest first):',
          sortedTrades.map(t => ({ id: t.trade.id, date: t.trade.date }))
        );
        const recentTradesData = sortedTrades.slice(0, 5);
        console.log(
          '📅 Recent trades data (top 5):',
          recentTradesData.map(t => ({ id: t.trade.id, date: t.trade.date }))
        );
        setChartData(generateChartData());
        setRecentTrades(recentTradesData);
        console.log('✅ Real data set successfully');
      } else {
        console.warn('⚠️ No trades found in IndexedDB, using mock data');
        setChartData(generateChartData());
        setRecentTrades(generateTradeData());
      }
      // TODO: Calculate real metrics from actual trade data
      // For now, keep mock metrics until we implement real calculations
      // setMetrics(calculateRealMetrics(completeTradeData));
    } catch (err) {
      console.error('❌ Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
      // Fallback to mock data if real data fails
      console.warn('🔄 Falling back to mock data due to error');
      setChartData(generateChartData());
      setRecentTrades(generateTradeData());
    } finally {
      setIsLoading(false);
      console.log('🏁 fetchDashboardData completed');
    }
  }, []);
  return {
    metrics,
    chartData,
    recentTrades,
    isLoading,
    error,
    fetchDashboardData,
  };
};
//# sourceMappingURL=useDashboardData.js.map
