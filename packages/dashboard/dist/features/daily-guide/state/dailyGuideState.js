/**
 * Daily Guide State
 *
 * State management for the daily guide feature.
 *
 * This module provides:
 * - State interface and initial state
 * - Action types and action creators
 * - Reducer function
 * - Context provider and hooks
 *
 * @example
 * ```tsx
 * // Wrap your component with the provider
 * <DailyGuideProvider>
 *   <YourComponent />
 * </DailyGuideProvider>
 *
 * // Use the state in your component
 * const { state, dispatch } = useDailyGuideStore();
 * const value = useDailyGuideSelector(selectSomething);
 * const action = useDailyGuideAction(dailyGuideActions.doSomething);
 * ```
 */
import { createStoreContext, persistState } from '@adhd-trading-dashboard/shared';
// Action types
export var DailyGuideActionTypes;
(function (DailyGuideActionTypes) {
  DailyGuideActionTypes['FETCH_DATA_START'] = 'dailyGuide/FETCH_DATA_START';
  DailyGuideActionTypes['FETCH_DATA_SUCCESS'] = 'dailyGuide/FETCH_DATA_SUCCESS';
  DailyGuideActionTypes['FETCH_DATA_ERROR'] = 'dailyGuide/FETCH_DATA_ERROR';
  DailyGuideActionTypes['UPDATE_TRADING_PLAN_ITEM'] = 'dailyGuide/UPDATE_TRADING_PLAN_ITEM';
  DailyGuideActionTypes['ADD_TRADING_PLAN_ITEM'] = 'dailyGuide/ADD_TRADING_PLAN_ITEM';
  DailyGuideActionTypes['REMOVE_TRADING_PLAN_ITEM'] = 'dailyGuide/REMOVE_TRADING_PLAN_ITEM';
  DailyGuideActionTypes['UPDATE_SELECTED_DATE'] = 'dailyGuide/UPDATE_SELECTED_DATE';
  DailyGuideActionTypes['UPDATE_MARKET_OVERVIEW'] = 'dailyGuide/UPDATE_MARKET_OVERVIEW';
  DailyGuideActionTypes['UPDATE_KEY_PRICE_LEVELS'] = 'dailyGuide/UPDATE_KEY_PRICE_LEVELS';
  DailyGuideActionTypes['UPDATE_PREFERENCES'] = 'dailyGuide/UPDATE_PREFERENCES';
  DailyGuideActionTypes['RESET_STATE'] = 'dailyGuide/RESET_STATE';
})(DailyGuideActionTypes || (DailyGuideActionTypes = {}));
// Initial state
export const initialDailyGuideState = {
  data: {
    marketOverview: null,
    tradingPlan: null,
    keyPriceLevels: [],
    watchlist: [],
    marketNews: [],
  },
  isLoading: false,
  error: null,
  selectedDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
};
// Reducer
export const dailyGuideReducer = (state, action) => {
  switch (action.type) {
    case DailyGuideActionTypes.FETCH_DATA_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case DailyGuideActionTypes.FETCH_DATA_SUCCESS:
      return {
        ...state,
        data: action.payload,
        isLoading: false,
        error: null,
      };
    case DailyGuideActionTypes.FETCH_DATA_ERROR:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM:
      if (!state.data.tradingPlan) {
        return state;
      }
      return {
        ...state,
        data: {
          ...state.data,
          tradingPlan: {
            ...state.data.tradingPlan,
            items: state.data.tradingPlan.items.map(item =>
              item.id === action.payload.id
                ? { ...item, completed: action.payload.completed }
                : item
            ),
          },
        },
      };
    case DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM:
      if (!state.data.tradingPlan) {
        return {
          ...state,
          data: {
            ...state.data,
            tradingPlan: {
              items: [action.payload],
              strategy: '',
              riskManagement: {
                maxRiskPerTrade: 0,
                maxDailyLoss: 0,
                maxTrades: 0,
                positionSizing: '',
              },
              notes: '',
            },
          },
        };
      }
      return {
        ...state,
        data: {
          ...state.data,
          tradingPlan: {
            ...state.data.tradingPlan,
            items: [...state.data.tradingPlan.items, action.payload],
          },
        },
      };
    case DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM:
      if (!state.data.tradingPlan) {
        return state;
      }
      return {
        ...state,
        data: {
          ...state.data,
          tradingPlan: {
            ...state.data.tradingPlan,
            items: state.data.tradingPlan.items.filter(item => item.id !== action.payload),
          },
        },
      };
    case DailyGuideActionTypes.UPDATE_SELECTED_DATE:
      return {
        ...state,
        selectedDate: action.payload,
      };
    case DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW:
      return {
        ...state,
        data: {
          ...state.data,
          marketOverview: action.payload,
        },
      };
    case DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS:
      return {
        ...state,
        data: {
          ...state.data,
          keyPriceLevels: action.payload,
        },
      };
    case DailyGuideActionTypes.RESET_STATE:
      return {
        ...initialDailyGuideState,
        selectedDate: state.selectedDate, // Preserve selected date
      };
    default:
      return state;
  }
};
// Persist state
const { reducer: persistedReducer, initialState: persistedInitialState } = persistState(
  dailyGuideReducer,
  {
    key: 'dailyGuide',
    initialState: initialDailyGuideState,
    version: 1,
    filter: state => ({
      // Only persist user preferences, not transient data
      selectedDate: state.selectedDate,
    }),
  }
);
// Create store context
export const {
  Context: DailyGuideContext,
  Provider: DailyGuideProvider,
  useStore: useDailyGuideStore,
  useSelector: useDailyGuideSelector,
  useAction: useDailyGuideAction,
  useActions: useDailyGuideActions,
} = createStoreContext(persistedReducer, persistedInitialState, 'DailyGuideContext');
// Action creators
export const dailyGuideActions = {
  fetchDataStart: () => ({
    type: DailyGuideActionTypes.FETCH_DATA_START,
  }),
  fetchDataSuccess: data => ({
    type: DailyGuideActionTypes.FETCH_DATA_SUCCESS,
    payload: data,
  }),
  fetchDataError: error => ({
    type: DailyGuideActionTypes.FETCH_DATA_ERROR,
    payload: error,
  }),
  updateTradingPlanItem: (id, completed) => ({
    type: DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM,
    payload: { id, completed },
  }),
  addTradingPlanItem: item => ({
    type: DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM,
    payload: item,
  }),
  removeTradingPlanItem: id => ({
    type: DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM,
    payload: id,
  }),
  updateSelectedDate: date => ({
    type: DailyGuideActionTypes.UPDATE_SELECTED_DATE,
    payload: date,
  }),
  updateMarketOverview: overview => ({
    type: DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW,
    payload: overview,
  }),
  updateKeyPriceLevels: levels => ({
    type: DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS,
    payload: levels,
  }),
  resetState: () => ({
    type: DailyGuideActionTypes.RESET_STATE,
  }),
};
//# sourceMappingURL=dailyGuideState.js.map
