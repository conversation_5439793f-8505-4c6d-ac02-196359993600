/**
 * Daily Guide Selectors
 *
 * Selectors for the daily guide feature.
 */
import { createSelector } from '@adhd-trading-dashboard/shared';
// Basic selectors
export const selectDailyGuideData = state => state.data;
export const selectMarketOverview = state => state.data.marketOverview;
export const selectTradingPlan = state => state.data.tradingPlan;
export const selectKeyPriceLevels = state => state.data.keyPriceLevels;
export const selectWatchlist = state => state.data.watchlist;
export const selectMarketNews = state => state.data.marketNews;
export const selectIsLoading = state => state.isLoading;
export const selectError = state => state.error;
export const selectSelectedDate = state => state.selectedDate;
// Derived selectors
export const selectTradingPlanItems = createSelector(
  selectTradingPlan,
  tradingPlan => tradingPlan?.items || []
);
export const selectCompletedTradingPlanItems = createSelector(selectTradingPlanItems, items =>
  items.filter(item => item.completed)
);
export const selectIncompleteTradingPlanItems = createSelector(selectTradingPlanItems, items =>
  items.filter(item => !item.completed)
);
export const selectTradingPlanCompletion = createSelector(
  selectTradingPlanItems,
  selectCompletedTradingPlanItems,
  (allItems, completedItems) => {
    if (allItems.length === 0) return 0;
    return completedItems.length / allItems.length;
  }
);
export const selectTradingPlanItemsByPriority = createSelector(selectTradingPlanItems, items => {
  const result = {
    high: [],
    medium: [],
    low: [],
  };
  items.forEach(item => {
    result[item.priority].push(item);
  });
  return result;
});
export const selectMarketIndices = createSelector(
  selectMarketOverview,
  marketOverview => marketOverview?.indices || []
);
export const selectPositiveIndices = createSelector(selectMarketIndices, indices =>
  indices.filter(index => index.change > 0)
);
export const selectNegativeIndices = createSelector(selectMarketIndices, indices =>
  indices.filter(index => index.change < 0)
);
export const selectMarketSentiment = createSelector(
  selectMarketOverview,
  marketOverview => marketOverview?.sentiment || 'neutral'
);
export const selectMarketSummary = createSelector(
  selectMarketOverview,
  marketOverview => marketOverview?.summary || ''
);
export const selectEconomicEvents = createSelector(
  selectMarketOverview,
  marketOverview => marketOverview?.economicEvents || []
);
export const selectHighImpactEconomicEvents = createSelector(selectEconomicEvents, events =>
  events.filter(event => event.importance === 'high')
);
export const selectKeyPriceLevelsBySymbol = createSelector(selectKeyPriceLevels, levels => {
  const result = {};
  levels.forEach(level => {
    result[level.symbol] = level;
  });
  return result;
});
export const selectWatchlistBySymbol = createSelector(selectWatchlist, watchlist => {
  const result = {};
  watchlist.forEach(item => {
    result[item.symbol] = item;
  });
  return result;
});
export const selectHighImpactMarketNews = createSelector(selectMarketNews, news =>
  news.filter(item => item.impact === 'high')
);
export const selectMarketNewsByDate = createSelector(selectMarketNews, news => {
  const result = {};
  news.forEach(item => {
    const date = new Date(item.timestamp).toISOString().split('T')[0];
    if (!result[date]) {
      result[date] = [];
    }
    result[date].push(item);
  });
  return result;
});
export const selectHasData = createSelector(
  selectMarketOverview,
  selectTradingPlan,
  selectKeyPriceLevels,
  (marketOverview, tradingPlan, keyPriceLevels) => {
    return !!marketOverview || !!tradingPlan || keyPriceLevels.length > 0;
  }
);
export const selectLastUpdated = createSelector(
  selectMarketOverview,
  marketOverview => marketOverview?.lastUpdated || null
);
// Export all selectors
export const dailyGuideSelectors = {
  selectDailyGuideData,
  selectMarketOverview,
  selectTradingPlan,
  selectKeyPriceLevels,
  selectWatchlist,
  selectMarketNews,
  selectIsLoading,
  selectError,
  selectSelectedDate,
  selectTradingPlanItems,
  selectCompletedTradingPlanItems,
  selectIncompleteTradingPlanItems,
  selectTradingPlanCompletion,
  selectTradingPlanItemsByPriority,
  selectMarketIndices,
  selectPositiveIndices,
  selectNegativeIndices,
  selectMarketSentiment,
  selectMarketSummary,
  selectEconomicEvents,
  selectHighImpactEconomicEvents,
  selectKeyPriceLevelsBySymbol,
  selectWatchlistBySymbol,
  selectHighImpactMarketNews,
  selectMarketNewsByDate,
  selectHasData,
  selectLastUpdated,
};
//# sourceMappingURL=dailyGuideSelectors.js.map
