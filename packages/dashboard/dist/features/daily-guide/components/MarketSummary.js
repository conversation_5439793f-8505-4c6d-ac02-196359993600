import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import styled from 'styled-components';
import { Badge } from '@adhd-trading-dashboard/shared';
// Styled components
const Header = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
const SentimentContainer = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const SentimentLabel = styled.span `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const LastUpdated = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const Summary = styled.p `
  font-size: ${({ theme }) => theme.fontSizes.md};
  line-height: 1.5;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
/**
 * Get the badge variant for a market sentiment
 */
const getSentimentVariant = (sentiment) => {
    switch (sentiment) {
        case 'bullish':
            return 'success';
        case 'bearish':
            return 'error';
        case 'neutral':
        default:
            return 'neutral';
    }
};
/**
 * Format a date string
 */
const formatDate = (dateString) => {
    if (!dateString)
        return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
    });
};
/**
 * Market Summary Component
 *
 * Displays market sentiment, summary text, and last updated information.
 */
export const MarketSummary = ({ sentiment, summary, lastUpdated, }) => {
    return (_jsxs(_Fragment, { children: [_jsxs(Header, { children: [_jsxs(SentimentContainer, { children: [_jsx(SentimentLabel, { children: "Market Sentiment:" }), _jsx(Badge, { variant: getSentimentVariant(sentiment), solid: true, children: sentiment.toUpperCase() })] }), _jsxs(LastUpdated, { children: ["Last updated: ", formatDate(lastUpdated)] })] }), _jsx(Summary, { children: summary })] }));
};
//# sourceMappingURL=MarketSummary.js.map