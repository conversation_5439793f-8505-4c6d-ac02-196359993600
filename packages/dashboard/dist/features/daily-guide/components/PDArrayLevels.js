import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { usePDArrayIntelligence } from '../hooks/usePDArrayIntelligence';
// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;
const SectionTitle = styled.h3`
  color: var(--session-text-primary);
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const PDArrayCard = styled.div.attrs(({ arrayType, isActive }) => ({
  className: `pd-array-card PDArrayCard ${isActive ? 'active' : ''}`,
  'data-array-type': arrayType.toLowerCase(),
  'data-active': isActive,
}))`
  /* Use CSS variables for clean theming */
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  &[data-active='true'] {
    border-left-color: var(--pd-array-accent, var(--session-card-accent));
    box-shadow: var(--shadow-sm), 0 0 0 1px var(--pd-array-accent, var(--session-card-accent));
  }

  /* Array type styling handled by CSS variables */
  &[data-array-type='fvg'][data-active='true'] {
    --pd-array-accent: var(--info-color);
  }

  &[data-array-type='nwog'][data-active='true'] {
    --pd-array-accent: var(--success-color);
  }

  &[data-array-type='ndog'][data-active='true'] {
    --pd-array-accent: var(--warning-color);
  }

  &[data-array-type='rd'][data-active='true'] {
    --pd-array-accent: var(--error-color);
  }

  &[data-array-type='liquidity'][data-active='true'] {
    --pd-array-accent: var(--secondary-color);
  }

  &[data-array-type='summary'][data-active='true'] {
    --pd-array-accent: var(--primary-color);
  }
`;
const ArrayHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;
const ArrayType = styled.div.attrs(({ arrayType }) => ({
  className: `array-type ArrayType`,
  'data-array-type': arrayType.toLowerCase(),
}))`
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  /* Use CSS variables for array type colors */
  &[data-array-type='fvg'] {
    color: var(--info-color);
  }

  &[data-array-type='nwog'] {
    color: var(--success-color);
  }

  &[data-array-type='ndog'] {
    color: var(--warning-color);
  }

  &[data-array-type='rd'] {
    color: var(--error-color);
  }

  &[data-array-type='liquidity'] {
    color: var(--secondary-color);
  }

  &[data-array-type='summary'] {
    color: var(--primary-color);
  }

  /* Default color */
  color: var(--session-text-primary);
`;
const ArrayStatus = styled.div.attrs(({ isActive }) => ({
  className: `array-status ArrayStatus`,
  'data-active': isActive,
}))`
  background: ${({ isActive }) =>
    isActive ? 'var(--success-color)' : 'var(--session-card-border)'};
  color: ${({ isActive }) =>
    isActive ? 'var(--session-text-primary)' : 'var(--session-text-secondary)'};
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;
const LevelInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 12px 0;
`;
const LevelDetail = styled.div`
  text-align: center;
`;
const LevelValue = styled.div`
  font-size: 16px;
  font-weight: 700;
  color: var(--session-text-primary);
`;
const LevelLabel = styled.div`
  font-size: 10px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
`;
const PerformanceStats = styled.div.attrs({
  className: 'performance-stats PerformanceStats',
})`
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
  box-shadow: var(--shadow-sm);
`;
const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
`;
const StatItem = styled.div`
  text-align: center;
`;
const StatValue = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: var(--session-text-primary);
`;
const StatLabel = styled.div`
  font-size: 9px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
`;
const PriorityBadge = styled.div.attrs(({ priority }) => ({
  className: 'priority-badge',
  'data-priority': priority,
}))`
  background: ${({ priority }) => {
    switch (priority) {
      case 'HIGH':
        return 'var(--error-color)';
      case 'MEDIUM':
        return 'var(--warning-color)';
      default:
        return 'var(--session-card-border)';
    }
  }};
  color: ${({ priority }) =>
    priority === 'LOW' ? 'var(--session-text-secondary)' : 'var(--session-text-primary)'};
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 8px;
`;
const RecommendationText = styled.div`
  color: var(--session-text-secondary);
  font-size: 12px;
  line-height: 1.4;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--session-card-border);
`;
const EmptyState = styled.div`
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 12px;
  text-align: center;
  padding: 40px 20px;
  color: var(--session-text-secondary);
`;
const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;
const EmptyTitle = styled.h3`
  color: var(--session-text-primary);
  font-size: 18px;
  margin-bottom: 8px;
`;
const EmptyMessage = styled.p`
  color: var(--session-text-secondary);
  font-size: 14px;
  line-height: 1.5;
`;
/**
 * PD Array Levels Component
 */
export const PDArrayLevels = ({ isLoading = false, error = null, onRefresh, className }) => {
  const {
    pdArrayIntelligence,
    isLoading: intelligenceLoading,
    error: intelligenceError,
  } = usePDArrayIntelligence();
  const loading = isLoading || intelligenceLoading;
  const displayError = error || intelligenceError;
  // Loading state
  if (loading) {
    return _jsx(Card, {
      title: '\uD83C\uDFAF PD Array Intelligence',
      children: _jsx('div', {
        style: { padding: '24px', textAlign: 'center' },
        children: 'Analyzing PD Array levels and liquidity targets...',
      }),
    });
  }
  // Error state
  if (displayError) {
    return _jsx(Card, {
      title: '\uD83C\uDFAF PD Array Intelligence',
      children: _jsxs('div', {
        style: { padding: '24px', textAlign: 'center', color: 'var(--error-color)' },
        children: [
          'Error: ',
          displayError,
          onRefresh &&
            _jsx('button', {
              onClick: onRefresh,
              style: {
                marginLeft: '16px',
                padding: '8px 16px',
                background: 'var(--session-card-bg)',
                border: '1px solid var(--session-card-border)',
                borderRadius: '4px',
                cursor: 'pointer',
                color: 'var(--session-text-primary)',
              },
              children: 'Retry',
            }),
        ],
      }),
    });
  }
  // Empty state
  if (!pdArrayIntelligence || pdArrayIntelligence.activePDArrays.length === 0) {
    return _jsx(Card, {
      title: '\uD83C\uDFAF PD Array Intelligence',
      children: _jsxs(EmptyState, {
        children: [
          _jsx(EmptyIcon, { children: '\uD83D\uDCCA' }),
          _jsx(EmptyTitle, { children: 'No PD Array Data Available' }),
          _jsx(EmptyMessage, {
            children:
              'Import your trading data to begin tracking FVGs, NWOG/NDOG levels, RD formations, and liquidity targets. The system will analyze your historical performance with each PD Array type.',
          }),
        ],
      }),
    });
  }
  return _jsx(Card, {
    title: '\uD83C\uDFAF PD Array Intelligence',
    actions: onRefresh
      ? _jsx('button', {
          onClick: onRefresh,
          style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' },
          children: '\uD83D\uDD04 Refresh',
        })
      : undefined,
    children: _jsxs(Container, {
      className: className,
      children: [
        _jsxs('div', {
          children: [
            _jsxs(SectionTitle, {
              children: [
                '\uD83D\uDD25 Active PD Arrays',
                _jsx(PriorityBadge, { priority: 'HIGH', children: 'PRIORITY TARGETS' }),
              ],
            }),
            pdArrayIntelligence.activePDArrays.map((array, index) =>
              _jsxs(
                PDArrayCard,
                {
                  arrayType: array.type,
                  isActive: array.isActive,
                  children: [
                    _jsxs(ArrayHeader, {
                      children: [
                        _jsx(ArrayType, { arrayType: array.type, children: array.type }),
                        _jsx(ArrayStatus, {
                          isActive: array.isActive,
                          children: array.isActive ? 'ACTIVE' : 'INACTIVE',
                        }),
                      ],
                    }),
                    _jsxs(LevelInfo, {
                      children: [
                        _jsxs(LevelDetail, {
                          children: [
                            _jsx(LevelValue, { children: array.level }),
                            _jsx(LevelLabel, { children: 'Level' }),
                          ],
                        }),
                        _jsxs(LevelDetail, {
                          children: [
                            _jsx(LevelValue, { children: array.timeframe }),
                            _jsx(LevelLabel, { children: 'Timeframe' }),
                          ],
                        }),
                        _jsxs(LevelDetail, {
                          children: [
                            _jsx(LevelValue, { children: array.age }),
                            _jsx(LevelLabel, { children: 'Age' }),
                          ],
                        }),
                      ],
                    }),
                    _jsx(PerformanceStats, {
                      children: _jsxs(StatsGrid, {
                        children: [
                          _jsxs(StatItem, {
                            children: [
                              _jsx(StatValue, { children: array.performance.totalTrades }),
                              _jsx(StatLabel, { children: 'Trades' }),
                            ],
                          }),
                          _jsxs(StatItem, {
                            children: [
                              _jsxs(StatValue, {
                                children: [array.performance.winRate.toFixed(0), '%'],
                              }),
                              _jsx(StatLabel, { children: 'Win Rate' }),
                            ],
                          }),
                          _jsxs(StatItem, {
                            children: [
                              _jsxs(StatValue, {
                                children: [array.performance.avgRMultiple.toFixed(1), 'R'],
                              }),
                              _jsx(StatLabel, { children: 'Avg R' }),
                            ],
                          }),
                          _jsxs(StatItem, {
                            children: [
                              _jsxs(StatValue, {
                                children: [array.performance.successRate.toFixed(0), '%'],
                              }),
                              _jsx(StatLabel, { children: 'Success Rate' }),
                            ],
                          }),
                        ],
                      }),
                    }),
                    _jsxs(RecommendationText, {
                      children: [
                        _jsx('strong', { children: 'Strategy:' }),
                        ' ',
                        array.recommendation,
                      ],
                    }),
                  ],
                },
                index
              )
            ),
          ],
        }),
        _jsxs('div', {
          children: [
            _jsx(SectionTitle, { children: '\uD83D\uDCCA PD Array Performance Summary' }),
            _jsxs(PDArrayCard, {
              arrayType: 'summary',
              isActive: true,
              children: [
                _jsxs(StatsGrid, {
                  children: [
                    _jsxs(StatItem, {
                      children: [
                        _jsx(StatValue, {
                          children: pdArrayIntelligence.summary.totalActiveLevels,
                        }),
                        _jsx(StatLabel, { children: 'Active Levels' }),
                      ],
                    }),
                    _jsxs(StatItem, {
                      children: [
                        _jsx(StatValue, {
                          children: pdArrayIntelligence.summary.bestPerformingType,
                        }),
                        _jsx(StatLabel, { children: 'Best Type' }),
                      ],
                    }),
                    _jsxs(StatItem, {
                      children: [
                        _jsxs(StatValue, {
                          children: [
                            pdArrayIntelligence.summary.overallSuccessRate.toFixed(0),
                            '%',
                          ],
                        }),
                        _jsx(StatLabel, { children: 'Overall Success' }),
                      ],
                    }),
                    _jsxs(StatItem, {
                      children: [
                        _jsxs(StatValue, {
                          children: [pdArrayIntelligence.summary.avgRMultiple.toFixed(1), 'R'],
                        }),
                        _jsx(StatLabel, { children: 'Avg R-Multiple' }),
                      ],
                    }),
                  ],
                }),
                _jsxs(RecommendationText, {
                  children: [
                    _jsx('strong', { children: 'Key Insights:' }),
                    ' ',
                    pdArrayIntelligence.summary.keyInsights.join(' • '),
                  ],
                }),
              ],
            }),
          ],
        }),
      ],
    }),
  });
};
//# sourceMappingURL=PDArrayLevels.js.map
