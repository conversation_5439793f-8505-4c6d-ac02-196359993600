import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Elite Intelligence Layout Component
 *
 * Progressive disclosure layout that addresses information overload by providing
 * quick decision-making with expandable detailed analysis. Implements ADHD-optimized
 * design patterns with expansion toggle and state-aware displays.
 */
import { Card } from '@adhd-trading-dashboard/shared';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useEnhancedSetupIntelligence } from '../hooks/useEnhancedSetupIntelligence';
import { useGranularSessionIntelligence } from '../hooks/useGranularSessionIntelligence';
import { useModelSelectionEngine } from '../hooks/useModelSelectionEngine';
import { usePatternQualityScoring } from '../hooks/usePatternQualityScoring';
import { useSuccessProbabilityCalculator } from '../hooks/useSuccessProbabilityCalculator';
import { DetailedAnalysisPanel } from './DetailedAnalysisPanel';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';
import { QuickDecisionPanel } from './QuickDecisionPanel';
// Styled components
const LayoutContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: 16px;
`;
const HeaderRight = styled.div `
  display: flex;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    justify-content: space-between;
  }
`;
const RefreshButton = styled.button `
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    border-color: var(--primary-color);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;
const ContentContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;
const ExpandedContent = styled.div `
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  opacity: ${({ $isExpanded }) => ($isExpanded ? 1 : 0)};
  max-height: ${({ $isExpanded }) => ($isExpanded ? '2000px' : '0')};
  margin-top: ${({ $isExpanded }) => ($isExpanded ? 'var(--spacing-md)' : '0')};
`;
const ExpandToggleButton = styled.button `
  background: var(--elite-section-bg);
  color: var(--text-primary);
  border: 1px solid var(--primary-color);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  box-shadow: var(--shadow-sm);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  text-transform: uppercase;

  &:hover:not(:disabled) {
    background: var(--elite-card-bg);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--accent-color);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &:active {
    transform: translateY(0);
  }

  .expand-icon {
    transition: transform 0.3s ease;
    transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : 'rotate(0deg)')};
    font-size: var(--font-size-xs);
  }
`;
const LoadingState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
  text-align: center;
`;
const ErrorState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--error-text);
  text-align: center;
`;
const LoadingSpinner = styled.div `
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
/**
 * Elite Intelligence Layout Component
 */
export const EliteIntelligenceLayout = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const marketState = getCurrentMarketState();
    // Hook data
    const { recommendation: modelRec, isLoading: modelLoading, error: modelError, } = useModelSelectionEngine();
    const { analysis: qualityAnalysis, isLoading: qualityLoading, error: qualityError, } = usePatternQualityScoring();
    const { isLoading: sessionLoading, error: sessionError } = useGranularSessionIntelligence();
    const { successProbability, isLoading: probabilityLoading } = useSuccessProbabilityCalculator(modelRec, qualityAnalysis.currentScore);
    const { setupIntelligence, isLoading: setupLoading, error: setupError, } = useEnhancedSetupIntelligence();
    // Aggregate loading and error states
    const aggregatedLoading = isLoading ||
        modelLoading ||
        qualityLoading ||
        sessionLoading ||
        probabilityLoading ||
        setupLoading;
    const aggregatedError = error || modelError || qualityError || sessionError || setupError;
    // Persist expansion preference
    useEffect(() => {
        const savedExpansion = localStorage.getItem('elite-intelligence-expanded');
        if (savedExpansion === 'true') {
            setIsExpanded(true);
        }
    }, []);
    const handleToggleExpansion = () => {
        const newExpanded = !isExpanded;
        setIsExpanded(newExpanded);
        localStorage.setItem('elite-intelligence-expanded', newExpanded.toString());
    };
    // Loading state
    if (aggregatedLoading) {
        return (_jsx(Card, { title: '\uD83E\uDDE0 Elite ICT Trading Intelligence', className: className, actions: onRefresh && (_jsx(RefreshButton, { onClick: onRefresh, disabled: aggregatedLoading, children: "\uD83D\uDD04 Refresh" })), children: _jsxs(LoadingState, { children: [_jsx(LoadingSpinner, {}), _jsx("div", { style: { fontSize: '16px', fontWeight: '500', marginBottom: '8px' }, children: "Analyzing Market Conditions" }), _jsx("div", { style: { fontSize: '14px', opacity: 0.7 }, children: "Generating intelligent trading recommendations..." })] }) }));
    }
    // Error state
    if (aggregatedError) {
        return (_jsx(Card, { title: '\uD83E\uDDE0 Elite ICT Trading Intelligence', className: className, actions: onRefresh && _jsx(RefreshButton, { onClick: onRefresh, children: "\uD83D\uDD04 Retry" }), children: _jsxs(ErrorState, { children: [_jsx("div", { style: { fontSize: '48px', marginBottom: '16px' }, children: "\u26A0\uFE0F" }), _jsx("div", { style: { fontSize: '16px', fontWeight: '500', marginBottom: '8px' }, children: "Error Loading Intelligence Data" }), _jsx("div", { style: { fontSize: '14px', opacity: 0.7 }, children: aggregatedError })] }) }));
    }
    return (_jsx(Card, { title: '\uD83E\uDDE0 Elite ICT Trading Intelligence', className: className, actions: _jsxs(HeaderRight, { children: [_jsx(MarketStateIndicator, { marketState: marketState, showDetails: true }), _jsxs(ExpandToggleButton, { "$isExpanded": isExpanded, onClick: handleToggleExpansion, disabled: aggregatedLoading, children: [isExpanded ? '📊 Collapse' : '📊 Detailed Analysis', _jsx("span", { className: 'expand-icon', children: "\u25BC" })] }), onRefresh && (_jsx(RefreshButton, { onClick: onRefresh, disabled: aggregatedLoading, children: "\uD83D\uDD04 Refresh" }))] }), children: _jsx(LayoutContainer, { children: _jsxs(ContentContainer, { children: [_jsx(QuickDecisionPanel, { modelRecommendation: modelRec, patternQuality: qualityAnalysis.currentScore, successProbability: successProbability, setupIntelligence: setupIntelligence }), _jsx(ExpandedContent, { "$isExpanded": isExpanded, children: _jsx(DetailedAnalysisPanel, { modelRecommendation: modelRec, patternQuality: qualityAnalysis.currentScore, successProbability: successProbability, setupIntelligence: setupIntelligence }) })] }) }) }));
};
export default EliteIntelligenceLayout;
//# sourceMappingURL=EliteIntelligenceLayout.js.map