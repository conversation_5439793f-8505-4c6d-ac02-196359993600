import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
import { Badge } from '@adhd-trading-dashboard/shared';
// Styled components
const EventsContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;
const EventsHeader = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.md};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
  color: ${({ theme }) => theme.colors.textPrimary};
  font-weight: 600;
`;
const EventsGrid = styled.div`
  display: grid;
  grid-template-columns: 80px 1fr 100px 100px 100px;
  gap: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  overflow: hidden;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0;
  }
`;
const EventsGridHeader = styled.div`
  font-weight: 600;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.surface};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  @media (max-width: 768px) {
    display: none;
  }
`;
const EventsGridRow = styled.div`
  display: contents;

  & > div {
    padding: ${({ theme }) => theme.spacing.sm};
    border-bottom: 1px solid ${({ theme }) => theme.colors.border};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    display: flex;
    align-items: center;
  }

  &:last-child > div {
    border-bottom: none;
  }

  &:hover > div {
    background-color: ${({ theme }) => theme.colors.hover};
  }

  @media (max-width: 768px) {
    display: block;
    border-bottom: 1px solid ${({ theme }) => theme.colors.border};

    &:last-child {
      border-bottom: none;
    }

    & > div {
      border-bottom: none;
      padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};

      &:before {
        content: attr(data-label);
        font-weight: 600;
        color: ${({ theme }) => theme.colors.textSecondary};
        margin-right: ${({ theme }) => theme.spacing.sm};
        min-width: 80px;
        display: inline-block;
      }
    }
  }
`;
const EventTitle = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  flex-wrap: wrap;
`;
const EventTime = styled.div`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const EventValue = styled.div`
  font-weight: ${({ isActual }) => (isActual ? '600' : '400')};
  color: ${({ theme, isActual }) =>
    isActual ? theme.colors.textPrimary : theme.colors.textSecondary};
`;
const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
/**
 * Market News Component
 *
 * Displays economic events and news in a structured table format.
 */
export const MarketNews = ({ events }) => {
  if (!events || events.length === 0) {
    return _jsxs(EventsContainer, {
      children: [
        _jsx(EventsHeader, { children: 'Economic Events' }),
        _jsx(EmptyState, { children: 'No economic events scheduled for today' }),
      ],
    });
  }
  return _jsxs(EventsContainer, {
    children: [
      _jsx(EventsHeader, { children: 'Economic Events' }),
      _jsxs(EventsGrid, {
        children: [
          _jsx(EventsGridHeader, { children: 'Time' }),
          _jsx(EventsGridHeader, { children: 'Event' }),
          _jsx(EventsGridHeader, { children: 'Expected' }),
          _jsx(EventsGridHeader, { children: 'Previous' }),
          _jsx(EventsGridHeader, { children: 'Actual' }),
          events.map((event, index) =>
            _jsxs(
              EventsGridRow,
              {
                children: [
                  _jsx(EventTime, { 'data-label': 'Time:', children: event.time }),
                  _jsxs(EventTitle, {
                    'data-label': 'Event:',
                    children: [
                      event.title,
                      event.importance === 'high' &&
                        _jsx(Badge, { variant: 'error', size: 'small', children: 'High Impact' }),
                      event.importance === 'medium' &&
                        _jsx(Badge, {
                          variant: 'warning',
                          size: 'small',
                          children: 'Medium Impact',
                        }),
                    ],
                  }),
                  _jsx(EventValue, { 'data-label': 'Expected:', children: event.expected || '-' }),
                  _jsx(EventValue, { 'data-label': 'Previous:', children: event.previous || '-' }),
                  _jsx(EventValue, {
                    'data-label': 'Actual:',
                    isActual: true,
                    children: event.actual || 'Pending',
                  }),
                ],
              },
              index
            )
          ),
        ],
      }),
    ],
  });
};
//# sourceMappingURL=MarketNews.js.map
