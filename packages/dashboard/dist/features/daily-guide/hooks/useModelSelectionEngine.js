/**
 * Model Selection Engine Hook
 *
 * Intelligent real-time model selection (RD-Cont vs FVG-RD) based on:
 * - Market volatility analysis
 * - Liquidity context assessment
 * - Timeframe confluence logic
 * - Previous session performance impact
 */
import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
/**
 * Calculate market volatility based on recent trade data
 */
const calculateVolatility = trades => {
  if (trades.length < 5) return 'medium';
  // Get last 10 trades
  const recentTrades = trades.slice(-10);
  const rMultiples = recentTrades
    .map(t => t.trade.r_multiple)
    .filter(r => r !== undefined && r !== null);
  if (rMultiples.length < 3) return 'medium';
  // Calculate standard deviation of R-multiples as volatility proxy
  const mean = rMultiples.reduce((sum, r) => sum + Math.abs(r), 0) / rMultiples.length;
  const variance =
    rMultiples.reduce((sum, r) => sum + Math.pow(Math.abs(r) - mean, 2), 0) / rMultiples.length;
  const stdDev = Math.sqrt(variance);
  // Classify volatility based on R-multiple standard deviation
  if (stdDev > 1.5) return 'high';
  if (stdDev > 0.8) return 'medium';
  return 'low';
};
/**
 * Assess liquidity context from trade notes and setup data
 */
const assessLiquidityContext = trades => {
  if (trades.length < 3) return 'mixed';
  const recentTrades = trades.slice(-5);
  let voidCount = 0;
  let reactionCount = 0;
  recentTrades.forEach(trade => {
    const notes = (trade.trade.notes || '').toLowerCase();
    const setup = (trade.trade.setup || '').toLowerCase();
    const combined = `${notes} ${setup}`;
    if (combined.includes('void') || combined.includes('gap') || combined.includes('imbalance')) {
      voidCount++;
    }
    if (
      combined.includes('reaction') ||
      combined.includes('liquidity') ||
      combined.includes('sweep')
    ) {
      reactionCount++;
    }
  });
  if (voidCount > reactionCount) return 'void';
  if (reactionCount > voidCount) return 'reaction';
  return 'mixed';
};
/**
 * Determine HTF trend from recent trade directions and success
 */
const determineHTFTrend = trades => {
  if (trades.length < 5) return 'neutral';
  const recentTrades = trades.slice(-10);
  const winningTrades = recentTrades.filter(t => t.trade.win_loss === 'Win');
  if (winningTrades.length < 3) return 'neutral';
  const longWins = winningTrades.filter(t => t.trade.direction === 'Long').length;
  const shortWins = winningTrades.filter(t => t.trade.direction === 'Short').length;
  if (longWins > shortWins * 1.5) return 'bullish';
  if (shortWins > longWins * 1.5) return 'bearish';
  return 'neutral';
};
/**
 * Get most successful model from recent session
 */
const getPreviousSessionSuccess = trades => {
  if (trades.length < 3) return null;
  // Get trades from last 24 hours
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const recentTrades = trades.filter(trade => {
    const tradeDate = new Date(trade.trade.date);
    return tradeDate >= yesterday;
  });
  if (recentTrades.length < 2) return null;
  const rdContWins = recentTrades.filter(
    t => t.trade.model_type === 'RD-Cont' && t.trade.win_loss === 'Win'
  ).length;
  const fvgRdWins = recentTrades.filter(
    t => t.trade.model_type === 'FVG-RD' && t.trade.win_loss === 'Win'
  ).length;
  if (rdContWins > fvgRdWins) return 'RD-Cont';
  if (fvgRdWins > rdContWins) return 'FVG-RD';
  return null;
};
/**
 * Model Selection Engine Hook
 */
export const useModelSelectionEngine = () => {
  const [trades, setTrades] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for model selection:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };
    fetchTrades();
  }, []);
  // Calculate model performance statistics
  const modelStats = useMemo(() => {
    const rdContTrades = trades.filter(t => t.trade.model_type === 'RD-Cont');
    const fvgRdTrades = trades.filter(t => t.trade.model_type === 'FVG-RD');
    const calculateStats = (modelTrades, model) => {
      const totalTrades = modelTrades.length;
      const winningTrades = modelTrades.filter(t => t.trade.win_loss === 'Win');
      const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
      const rMultiples = modelTrades
        .map(t => t.trade.r_multiple)
        .filter(r => r !== undefined && r !== null);
      const avgRMultiple =
        rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
      // Recent performance (last 10 trades)
      const recentTrades = modelTrades.slice(-10);
      const recentWins = recentTrades.filter(t => t.trade.win_loss === 'Win').length;
      const recentPerformance =
        recentTrades.length > 0 ? (recentWins / recentTrades.length) * 100 : 0;
      // Volatility performance analysis
      const volatilityPerformance = {
        low: { winRate: 0, avgR: 0, trades: 0 },
        medium: { winRate: 0, avgR: 0, trades: 0 },
        high: { winRate: 0, avgR: 0, trades: 0 },
      };
      // This is a simplified volatility classification - in real implementation,
      // you'd want to classify each trade's market conditions at the time
      const lowVolTrades = modelTrades.filter(t => (t.trade.r_multiple || 0) <= 1);
      const highVolTrades = modelTrades.filter(t => (t.trade.r_multiple || 0) >= 2);
      const medVolTrades = modelTrades.filter(t => {
        const r = t.trade.r_multiple || 0;
        return r > 1 && r < 2;
      });
      [
        { trades: lowVolTrades, key: 'low' },
        { trades: medVolTrades, key: 'medium' },
        { trades: highVolTrades, key: 'high' },
      ].forEach(({ trades: volTrades, key }) => {
        const wins = volTrades.filter(t => t.trade.win_loss === 'Win').length;
        const rs = volTrades.map(t => t.trade.r_multiple).filter(r => r !== undefined);
        volatilityPerformance[key] = {
          winRate: volTrades.length > 0 ? (wins / volTrades.length) * 100 : 0,
          avgR: rs.length > 0 ? rs.reduce((sum, r) => sum + r, 0) / rs.length : 0,
          trades: volTrades.length,
        };
      });
      return {
        model,
        totalTrades,
        winRate,
        avgRMultiple,
        recentPerformance,
        volatilityPerformance,
      };
    };
    return {
      'RD-Cont': calculateStats(rdContTrades, 'RD-Cont'),
      'FVG-RD': calculateStats(fvgRdTrades, 'FVG-RD'),
    };
  }, [trades]);
  // Generate intelligent model recommendation
  const recommendation = useMemo(() => {
    if (trades.length < 5) {
      return {
        recommendedModel: 'RD-Cont',
        probability: 60,
        confidence: 'LOW',
        reasoning: 'Insufficient data for analysis. RD-Cont recommended as default.',
        alternativeModel: 'FVG-RD',
        alternativeCondition: 'if clear FVG setups present',
        marketConditions: {
          volatility: 'medium',
          liquidityContext: 'mixed',
          htfTrend: 'neutral',
          previousSessionSuccess: null,
        },
      };
    }
    // Analyze current market conditions
    const volatility = calculateVolatility(trades);
    const liquidityContext = assessLiquidityContext(trades);
    const htfTrend = determineHTFTrend(trades);
    const previousSessionSuccess = getPreviousSessionSuccess(trades);
    const marketConditions = {
      volatility,
      liquidityContext,
      htfTrend,
      previousSessionSuccess,
    };
    // Decision logic based on conditions
    let score = 0; // Positive = FVG-RD, Negative = RD-Cont
    const reasons = [];
    // Volatility analysis
    if (volatility === 'high') {
      score += 2;
      reasons.push('High volatility favors FVG-RD (higher R-multiple potential)');
    } else if (volatility === 'low') {
      score -= 2;
      reasons.push('Low volatility favors RD-Cont (higher win rate)');
    }
    // Liquidity context
    if (liquidityContext === 'void') {
      score += 1.5;
      reasons.push('Liquidity voids present favor FVG-RD targeting fills');
    } else if (liquidityContext === 'reaction') {
      score -= 1.5;
      reasons.push('Strong liquidity reactions favor RD-Cont continuation patterns');
    }
    // HTF trend alignment
    if (htfTrend !== 'neutral') {
      score -= 1;
      reasons.push('Clear HTF trend structure favors RD-Cont within established trends');
    }
    // Previous session performance
    if (previousSessionSuccess === 'RD-Cont') {
      score -= 0.5;
      reasons.push('Recent RD-Cont success adds slight bias');
    } else if (previousSessionSuccess === 'FVG-RD') {
      score += 0.5;
      reasons.push('Recent FVG-RD success adds slight bias');
    }
    // Model performance comparison
    const rdContStats = modelStats['RD-Cont'];
    const fvgRdStats = modelStats['FVG-RD'];
    if (rdContStats.winRate > fvgRdStats.winRate + 10) {
      score -= 1;
      reasons.push(
        `RD-Cont shows superior win rate (${rdContStats.winRate.toFixed(
          1
        )}% vs ${fvgRdStats.winRate.toFixed(1)}%)`
      );
    } else if (fvgRdStats.avgRMultiple > rdContStats.avgRMultiple + 0.5) {
      score += 1;
      reasons.push(
        `FVG-RD shows superior R-multiple (${fvgRdStats.avgRMultiple.toFixed(
          1
        )} vs ${rdContStats.avgRMultiple.toFixed(1)})`
      );
    }
    // Determine recommendation
    const recommendedModel = score > 0 ? 'FVG-RD' : 'RD-Cont';
    const alternativeModel = score > 0 ? 'RD-Cont' : 'FVG-RD';
    // Calculate probability and confidence
    const absScore = Math.abs(score);
    const probability = Math.min(50 + absScore * 8, 85); // 50-85% range
    let confidence;
    if (absScore >= 3) confidence = 'HIGH';
    else if (absScore >= 1.5) confidence = 'MEDIUM';
    else confidence = 'LOW';
    const reasoning = reasons.join('; ');
    const alternativeCondition =
      recommendedModel === 'FVG-RD'
        ? 'if pattern quality <3.5 or low volatility'
        : 'if high volatility + clear FVG confluence';
    return {
      recommendedModel,
      probability,
      confidence,
      reasoning,
      alternativeModel,
      alternativeCondition,
      marketConditions,
    };
  }, [trades, modelStats]);
  return {
    recommendation,
    modelStats,
    isLoading,
    error,
    refresh: () => {
      setTrades([]);
    },
  };
};
//# sourceMappingURL=useModelSelectionEngine.js.map
