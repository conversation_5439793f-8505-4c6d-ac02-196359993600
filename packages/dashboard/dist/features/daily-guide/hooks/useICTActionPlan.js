/**
 * ICT Action Plan Hook
 *
 * Generates data-driven ICT action items based on real trading performance.
 * Provides model recommendations, risk management, and quality control guidance.
 */
import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
/**
 * Calculate weekly performance from recent trades
 */
const calculateWeeklyPerformance = trades => {
  // Get trades from last 7 days
  const weekAgo = new Date();
  weekAgo.setDate(weekAgo.getDate() - 7);
  const weeklyTrades = trades.filter(trade => {
    const tradeDate = new Date(trade.trade.date);
    return tradeDate >= weekAgo;
  });
  const totalTrades = weeklyTrades.length;
  const winningTrades = weeklyTrades.filter(t => t.trade.win_loss === 'Win').length;
  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
  const rMultiples = weeklyTrades
    .map(t => t.trade.r_multiple)
    .filter(r => r !== undefined && r !== null);
  const avgRMultiple =
    rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
  const qualities = weeklyTrades
    .map(t => t.trade.pattern_quality_rating)
    .filter(q => q !== undefined && q !== null);
  const avgQuality =
    qualities.length > 0 ? qualities.reduce((sum, q) => sum + q, 0) / qualities.length : 0;
  const totalPnL = weeklyTrades.map(t => t.trade.achieved_pl || 0).reduce((sum, pl) => sum + pl, 0);
  return {
    totalTrades,
    winRate,
    avgRMultiple,
    avgQuality,
    totalPnL,
  };
};
/**
 * Analyze model strategy and generate recommendations
 */
const analyzeModelStrategy = trades => {
  const rdContTrades = trades.filter(t => t.trade.model_type === 'RD-Cont');
  const fvgRdTrades = trades.filter(t => t.trade.model_type === 'FVG-RD');
  // RD-Cont analysis
  const rdContWins = rdContTrades.filter(t => t.trade.win_loss === 'Win').length;
  const rdContWinRate = rdContTrades.length > 0 ? (rdContWins / rdContTrades.length) * 100 : 0;
  const rdContRs = rdContTrades.map(t => t.trade.r_multiple).filter(r => r !== undefined);
  const rdContAvgR =
    rdContRs.length > 0 ? rdContRs.reduce((sum, r) => sum + r, 0) / rdContRs.length : 0;
  // FVG-RD analysis
  const fvgRdWins = fvgRdTrades.filter(t => t.trade.win_loss === 'Win').length;
  const fvgRdWinRate = fvgRdTrades.length > 0 ? (fvgRdWins / fvgRdTrades.length) * 100 : 0;
  const fvgRdRs = fvgRdTrades.map(t => t.trade.r_multiple).filter(r => r !== undefined);
  const fvgRdAvgR =
    fvgRdRs.length > 0 ? fvgRdRs.reduce((sum, r) => sum + r, 0) / fvgRdRs.length : 0;
  // Determine recommendation
  let recommendation = 'Either';
  let reasoning = '';
  if (rdContTrades.length >= 3 && fvgRdTrades.length >= 3) {
    if (rdContWinRate > fvgRdWinRate + 10) {
      recommendation = 'RD-Cont';
      reasoning = `RD-Cont shows superior performance (${rdContWinRate.toFixed(
        0
      )}% vs ${fvgRdWinRate.toFixed(0)}% win rate)`;
    } else if (fvgRdWinRate > rdContWinRate + 10) {
      recommendation = 'FVG-RD';
      reasoning = `FVG-RD shows superior performance (${fvgRdWinRate.toFixed(
        0
      )}% vs ${rdContWinRate.toFixed(0)}% win rate)`;
    } else if (fvgRdAvgR > rdContAvgR + 0.5) {
      recommendation = 'FVG-RD';
      reasoning = `FVG-RD offers better R-multiple potential (${fvgRdAvgR.toFixed(
        1
      )} vs ${rdContAvgR.toFixed(1)})`;
    } else {
      recommendation = 'RD-Cont';
      reasoning = `RD-Cont provides more consistent results (${rdContWinRate.toFixed(
        0
      )}% win rate)`;
    }
  } else if (rdContTrades.length >= 5) {
    recommendation = 'RD-Cont';
    reasoning = `Focus on RD-Cont (sufficient data: ${
      rdContTrades.length
    } trades, ${rdContWinRate.toFixed(0)}% win rate)`;
  } else if (fvgRdTrades.length >= 5) {
    recommendation = 'FVG-RD';
    reasoning = `Focus on FVG-RD (sufficient data: ${
      fvgRdTrades.length
    } trades, ${fvgRdWinRate.toFixed(0)}% win rate)`;
  } else {
    reasoning = 'Insufficient data for specific model recommendation - focus on quality setups';
  }
  return {
    recommendation,
    reasoning,
    rdCont: {
      trades: rdContTrades.length,
      winRate: rdContWinRate,
      avgR: rdContAvgR,
    },
    fvgRd: {
      trades: fvgRdTrades.length,
      winRate: fvgRdWinRate,
      avgR: fvgRdAvgR,
    },
  };
};
/**
 * Generate action items based on performance analysis
 */
const generateActionItems = (weeklyPerformance, modelStrategy, trades) => {
  const actionItems = [];
  // Model-based recommendations
  if (modelStrategy.recommendation !== 'Either') {
    const winRate =
      modelStrategy.recommendation === 'RD-Cont'
        ? modelStrategy.rdCont.winRate
        : modelStrategy.fvgRd.winRate;
    actionItems.push({
      description: `Focus on ${modelStrategy.recommendation} setups (${winRate.toFixed(
        0
      )}% recent win rate)`,
      priority: 'HIGH',
      category: 'MODEL',
    });
  }
  // Quality-based recommendations
  if (weeklyPerformance.avgQuality < 3.5) {
    actionItems.push({
      description: `Maintain pattern quality >3.5 (current avg: ${weeklyPerformance.avgQuality.toFixed(
        1
      )})`,
      priority: 'HIGH',
      category: 'QUALITY',
    });
  } else if (weeklyPerformance.avgQuality >= 4.0) {
    actionItems.push({
      description: `Excellent quality maintenance (${weeklyPerformance.avgQuality.toFixed(
        1
      )} avg) - continue standards`,
      priority: 'MEDIUM',
      category: 'QUALITY',
    });
  }
  // Session optimization
  const sessionAnalysis = analyzeSessionPerformance(trades);
  if (sessionAnalysis.bestSession) {
    actionItems.push({
      description: `Optimize ${
        sessionAnalysis.bestSession
      } timing (${sessionAnalysis.bestWinRate.toFixed(0)}% win rate)`,
      priority: 'MEDIUM',
      category: 'SESSION',
    });
  }
  // Risk management
  if (weeklyPerformance.avgRMultiple < 1.0) {
    actionItems.push({
      description: `Improve R-multiple targeting (current: ${weeklyPerformance.avgRMultiple.toFixed(
        1
      )}, target: 1.5+)`,
      priority: 'HIGH',
      category: 'RISK',
    });
  }
  // Win rate optimization
  if (weeklyPerformance.winRate < 60) {
    actionItems.push({
      description: `Focus on setup quality over quantity (${weeklyPerformance.winRate.toFixed(
        0
      )}% win rate)`,
      priority: 'HIGH',
      category: 'QUALITY',
    });
  }
  // PD Array recommendations
  const pdArrayAnalysis = analyzePDArrayUsage(trades);
  if (pdArrayAnalysis.bestType) {
    actionItems.push({
      description: `Target ${
        pdArrayAnalysis.bestType
      } parent PD arrays (${pdArrayAnalysis.bestWinRate.toFixed(0)}% success rate)`,
      priority: 'MEDIUM',
      category: 'MODEL',
    });
  }
  return actionItems.slice(0, 6); // Limit to top 6 items
};
/**
 * Analyze session performance to find best sessions
 */
const analyzeSessionPerformance = trades => {
  const sessions = ['NY Open', 'Lunch Macro', 'MOC', 'Pre-Market'];
  let bestSession = '';
  let bestWinRate = 0;
  sessions.forEach(session => {
    const sessionTrades = trades.filter(t => t.trade.session === session);
    if (sessionTrades.length >= 3) {
      const wins = sessionTrades.filter(t => t.trade.win_loss === 'Win').length;
      const winRate = (wins / sessionTrades.length) * 100;
      if (winRate > bestWinRate) {
        bestWinRate = winRate;
        bestSession = session;
      }
    }
  });
  return { bestSession, bestWinRate };
};
/**
 * Analyze PD Array usage patterns
 */
const analyzePDArrayUsage = trades => {
  const pdArrayTypes = ['NWOG', 'NDOG', 'FVG'];
  let bestType = '';
  let bestWinRate = 0;
  pdArrayTypes.forEach(type => {
    const typeTrades = trades.filter(trade => {
      const notes = trade.trade.notes?.toLowerCase() || '';
      const setup = trade.trade.setup?.toLowerCase() || '';
      return notes.includes(type.toLowerCase()) || setup.includes(type.toLowerCase());
    });
    if (typeTrades.length >= 3) {
      const wins = typeTrades.filter(t => t.trade.win_loss === 'Win').length;
      const winRate = (wins / typeTrades.length) * 100;
      if (winRate > bestWinRate) {
        bestWinRate = winRate;
        bestType = type;
      }
    }
  });
  return { bestType, bestWinRate };
};
/**
 * Calculate risk management parameters
 */
const calculateRiskManagement = trades => {
  const riskPoints = trades.map(t => t.trade.risk_points || 0).filter(r => r > 0);
  const avgRisk =
    riskPoints.length > 0 ? riskPoints.reduce((sum, r) => sum + r, 0) / riskPoints.length : 30;
  const rMultiples = trades.map(t => t.trade.r_multiple).filter(r => r !== undefined && r !== null);
  const avgR =
    rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 1.5;
  const targetRMultiple = Math.max(1.5, avgR);
  // Determine position sizing based on recent performance
  const recentTrades = trades.slice(-10);
  const recentWinRate =
    recentTrades.length > 0
      ? (recentTrades.filter(t => t.trade.win_loss === 'Win').length / recentTrades.length) * 100
      : 0;
  let positionSizing = 'Standard';
  if (recentWinRate >= 70) positionSizing = 'Aggressive';
  else if (recentWinRate < 50) positionSizing = 'Conservative';
  const stopLossStrategy = avgRisk > 0 ? `${avgRisk.toFixed(0)} point max` : '0.8x risk';
  return {
    avgRisk,
    targetRMultiple,
    positionSizing,
    stopLossStrategy,
  };
};
/**
 * Generate quality control checklist
 */
const generateQualityChecklist = trades => {
  const checklist = [
    'PD Array Confluence: 2+ elements present',
    'Parent PD Array: NWOG/NDOG reaction opportunity',
    'Entry Timing: Within optimal session windows',
    'Risk Management: 30 point max risk, 1.5R+ target',
  ];
  // Add dynamic items based on performance
  const avgQuality = trades
    .map(t => t.trade.pattern_quality_rating)
    .filter(q => q !== undefined && q !== null)
    .reduce((sum, q, _, arr) => sum + q / arr.length, 0);
  if (avgQuality > 0) {
    checklist.push(
      `Pattern Quality: >${Math.max(3.5, avgQuality - 0.2).toFixed(1)} rating required`
    );
  } else {
    checklist.push('Pattern Quality: >3.5 rating required');
  }
  return checklist;
};
/**
 * ICT Action Plan Hook
 */
export const useICTActionPlan = () => {
  const [trades, setTrades] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for ICT action plan:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };
    fetchTrades();
  }, []);
  // Generate action plan
  const actionPlan = useMemo(() => {
    if (trades.length === 0) {
      return null;
    }
    const weeklyPerformance = calculateWeeklyPerformance(trades);
    const modelStrategy = analyzeModelStrategy(trades);
    const actionItems = generateActionItems(weeklyPerformance, modelStrategy, trades);
    const riskManagement = calculateRiskManagement(trades);
    const qualityChecklist = generateQualityChecklist(trades);
    // Generate session guidance
    const sessionGuidance = [
      'Pre-Market: Selective, high-quality only',
      'NY Open: RD-Cont focus, 9:45-10:15 optimal window',
      'Lunch Macro: Either model, 11:50-12:10 prime time',
      'MOC: RD-Cont preferred, target higher R-multiples',
    ];
    return {
      weeklyPerformance,
      modelStrategy,
      actionItems,
      riskManagement,
      qualityChecklist,
      sessionGuidance,
    };
  }, [trades]);
  return {
    actionPlan,
    isLoading,
    error,
    refresh: () => {
      setTrades([]);
    },
  };
};
//# sourceMappingURL=useICTActionPlan.js.map
