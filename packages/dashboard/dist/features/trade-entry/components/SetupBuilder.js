import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Setup Builder Component
 *
 * Modular setup construction matrix that replaces complex dropdown-based
 * setup classification with atomic elements that can be combined infinitely.
 */
import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { SETUP_ELEMENTS } from '@adhd-trading-dashboard/shared';
// F1 Racing Theme Styled Components
const BuilderContainer = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
`;
const SectionTitle = styled.h3`
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
`;
const MatrixGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
`;
const ElementSection = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 16px;
`;
const ElementTitle = styled.h4`
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const Select = styled.select`
  width: 100%;
  padding: 10px 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  color: #ffffff;
  font-size: 0.9rem;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color) 33;
  }

  option {
    background: var(--bg-primary);
    color: #ffffff;
  }
`;
const PreviewContainer = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
`;
const PreviewText = styled.div`
  color: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  min-height: 20px;
`;
const RequiredIndicator = styled.span`
  color: var(--primary-color);
  margin-left: 4px;
`;
const OptionalIndicator = styled.span`
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-left: 4px;
`;
const SetupBuilder = ({ onSetupChange, initialComponents }) => {
  const [components, setComponents] = useState({
    constant: initialComponents?.constant || '',
    action: initialComponents?.action || 'None',
    variable: initialComponents?.variable || 'None',
    entry: initialComponents?.entry || '',
  });
  // Update parent component when components change
  useEffect(() => {
    if (components.constant && components.entry) {
      onSetupChange(components);
    }
  }, [components, onSetupChange]);
  const handleComponentChange = (elementType, value) => {
    setComponents(prev => ({
      ...prev,
      [elementType]: value,
    }));
  };
  // Generate setup description preview
  const generatePreview = () => {
    const parts = [];
    if (components.constant) {
      parts.push(components.constant);
    }
    if (components.action && components.action !== 'None') {
      parts.push(`→ ${components.action}`);
    }
    if (components.variable && components.variable !== 'None') {
      parts.push(`+ ${components.variable}`);
    }
    if (components.entry) {
      parts.push(`[${components.entry}]`);
    }
    return parts.join(' ') || 'Select required elements to preview setup...';
  };
  return _jsxs(BuilderContainer, {
    children: [
      _jsx(SectionTitle, { children: '\uD83C\uDFD7\uFE0F Setup Construction Matrix' }),
      _jsxs(MatrixGrid, {
        children: [
          _jsxs(ElementSection, {
            children: [
              _jsxs(ElementTitle, {
                children: ['Constant Element', _jsx(RequiredIndicator, { children: '*' })],
              }),
              _jsxs(Select, {
                value: components.constant,
                onChange: e => handleComponentChange('constant', e.target.value),
                children: [
                  _jsx('option', { value: '', children: 'Select Constant Element' }),
                  _jsx('optgroup', {
                    label: 'Parent Arrays',
                    children: SETUP_ELEMENTS.constant.parentArrays.map(option =>
                      _jsx('option', { value: option, children: option }, option)
                    ),
                  }),
                  _jsx('optgroup', {
                    label: 'FVG Types',
                    children: SETUP_ELEMENTS.constant.fvgTypes.map(option =>
                      _jsx('option', { value: option, children: option }, option)
                    ),
                  }),
                ],
              }),
            ],
          }),
          _jsxs(ElementSection, {
            children: [
              _jsxs(ElementTitle, {
                children: ['Action Element', _jsx(OptionalIndicator, { children: '(optional)' })],
              }),
              _jsx(Select, {
                value: components.action,
                onChange: e => handleComponentChange('action', e.target.value),
                children: SETUP_ELEMENTS.action.liquidityEvents.map(option =>
                  _jsx('option', { value: option, children: option }, option)
                ),
              }),
            ],
          }),
          _jsxs(ElementSection, {
            children: [
              _jsxs(ElementTitle, {
                children: ['Variable Element', _jsx(OptionalIndicator, { children: '(optional)' })],
              }),
              _jsx(Select, {
                value: components.variable,
                onChange: e => handleComponentChange('variable', e.target.value),
                children: SETUP_ELEMENTS.variable.rdTypes.map(option =>
                  _jsx('option', { value: option, children: option }, option)
                ),
              }),
            ],
          }),
          _jsxs(ElementSection, {
            children: [
              _jsxs(ElementTitle, {
                children: ['Entry Method', _jsx(RequiredIndicator, { children: '*' })],
              }),
              _jsxs(Select, {
                value: components.entry,
                onChange: e => handleComponentChange('entry', e.target.value),
                children: [
                  _jsx('option', { value: '', children: 'Select Entry Method' }),
                  SETUP_ELEMENTS.entry.methods.map(option =>
                    _jsx('option', { value: option, children: option }, option)
                  ),
                ],
              }),
            ],
          }),
        ],
      }),
      _jsxs(PreviewContainer, {
        children: [
          _jsx(ElementTitle, { children: 'Setup Preview' }),
          _jsx(PreviewText, { children: generatePreview() }),
        ],
      }),
    ],
  });
};
export default SetupBuilder;
//# sourceMappingURL=SetupBuilder.js.map
