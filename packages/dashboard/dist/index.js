import { jsx as _jsx } from "react/jsx-runtime";
/**
 * ADHD Trading Dashboard - Main Entry Point
 */
// Import DevTools configuration first
import './devtools-config';
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import reportWebVitals from './reportWebVitals';
import './styles/variables.css';
import './styles/global.css';
import './styles/f1-theme.css';
// IMPORTANT: All code must be after imports
const main = () => {
    console.log('ADHD Trading Dashboard initializing...');
    // Get the root element
    const rootElement = document.getElementById('root');
    // Create a fallback root element if needed
    if (!rootElement) {
        console.error('Root element not found, creating a fallback element');
        const fallbackRoot = document.createElement('div');
        fallbackRoot.id = 'root';
        document.body.appendChild(fallbackRoot);
    }
    // Create the React root
    const root = ReactDOM.createRoot(document.getElementById('root'));
    // Render the app
    root.render(_jsx(React.StrictMode, { children: _jsx(App, {}) }));
    // Performance measurement
    reportWebVitals();
};
// Simple error handler
window.addEventListener('error', (event) => {
    console.error('Error:', event.error || event.message);
});
// Execute the main function
main();
//# sourceMappingURL=index.js.map