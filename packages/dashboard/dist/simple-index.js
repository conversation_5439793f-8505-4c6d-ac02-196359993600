import { jsx as _jsx } from "react/jsx-runtime";
// Import DevTools configuration first
import './devtools-config';
import React from 'react';
import ReactDOM from 'react-dom/client';
// import SimpleApp from './SimpleApp'; // Removed as file doesn't exist
// Get the root element
const rootElement = document.getElementById('root');
// Create a fallback root element if needed
if (!rootElement) {
    console.error('Root element not found, creating a fallback element');
    const fallbackRoot = document.createElement('div');
    fallbackRoot.id = 'root';
    document.body.appendChild(fallbackRoot);
}
// Create the React root
const root = ReactDOM.createRoot(document.getElementById('root'));
// Render the app
root.render(_jsx(React.StrictMode, { children: _jsx("div", { children: "Simple App placeholder - SimpleApp component not found" }) }));
//# sourceMappingURL=simple-index.js.map