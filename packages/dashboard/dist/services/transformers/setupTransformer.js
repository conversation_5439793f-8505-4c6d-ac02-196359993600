/**
 * Setup Transformer Service
 *
 * Transforms setup components to human-readable descriptions and handles
 * backward compatibility with existing string-based setup descriptions.
 */
export class SetupTransformer {
  /**
   * Convert setup components to a human-readable description
   * @param components The setup components to transform
   * @returns A formatted setup description string
   */
  static componentsToDescription(components) {
    const parts = [];
    // Add constant element (required)
    if (components.constant) {
      parts.push(components.constant);
    }
    // Add action element with arrow indicator (optional)
    if (components.action && components.action !== 'None') {
      parts.push(`→ ${components.action}`);
    }
    // Add variable element with plus indicator (optional)
    if (components.variable && components.variable !== 'None') {
      parts.push(`+ ${components.variable}`);
    }
    // Add entry method in brackets (required)
    if (components.entry) {
      parts.push(`[${components.entry}]`);
    }
    return parts.join(' ');
  }
  /**
   * Parse a setup description string back to components (for backward compatibility)
   * @param description The setup description string to parse
   * @returns Parsed setup components or null if parsing fails
   */
  static descriptionToComponents(description) {
    if (!description || typeof description !== 'string') {
      return null;
    }
    try {
      // Initialize components
      const components = {
        constant: '',
        action: 'None',
        variable: 'None',
        entry: '',
      };
      // Extract entry method (in brackets)
      const entryMatch = description.match(/\[([^\]]+)\]/);
      if (entryMatch) {
        components.entry = entryMatch[1];
        description = description.replace(entryMatch[0], '').trim();
      }
      // Extract variable element (after +)
      const variableMatch = description.match(/\+\s*([^→]+?)(?=\s*→|$)/);
      if (variableMatch) {
        components.variable = variableMatch[1].trim();
        description = description.replace(variableMatch[0], '').trim();
      }
      // Extract action element (after →)
      const actionMatch = description.match(/→\s*([^+]+?)(?=\s*\+|$)/);
      if (actionMatch) {
        components.action = actionMatch[1].trim();
        description = description.replace(actionMatch[0], '').trim();
      }
      // Remaining text is the constant element
      if (description) {
        components.constant = description.trim();
      }
      // Validate that we have required elements
      if (components.constant && components.entry) {
        return components;
      }
      return null;
    } catch (error) {
      console.warn('Failed to parse setup description:', description, error);
      return null;
    }
  }
  /**
   * Check if a setup description is in the new component format
   * @param description The setup description to check
   * @returns True if it appears to be in component format
   */
  static isComponentFormat(description) {
    if (!description || typeof description !== 'string') {
      return false;
    }
    // Check for component format indicators
    const hasEntry = /\[([^\]]+)\]/.test(description);
    // Must have entry method, and optionally action/variable indicators
    return hasEntry;
  }
  /**
   * Get a display-friendly version of setup components
   * @param components The setup components
   * @returns A formatted display string
   */
  static getDisplayString(components) {
    const description = this.componentsToDescription(components);
    if (!description) {
      return 'No setup configured';
    }
    return description;
  }
  /**
   * Convert setup components to a short display string for tables
   * @param components The setup components
   * @returns A short formatted string for table display
   */
  static getShortDisplayString(components) {
    if (!components || !components.constant || !components.entry) {
      return 'No setup';
    }
    const parts = [];
    // Add constant (shortened)
    const constant = components.constant.replace('-FVG', '').replace('Top/Bottom-', '');
    parts.push(constant);
    // Add entry method (shortened)
    const entry = components.entry.replace('-Entry', '');
    parts.push(`[${entry}]`);
    return parts.join(' ');
  }
  /**
   * Convert database fields back to SetupComponents
   * @param setup_constant Database constant field
   * @param setup_action Database action field
   * @param setup_variable Database variable field
   * @param setup_entry Database entry field
   * @returns SetupComponents object
   */
  static fromDatabaseFields(setup_constant, setup_action, setup_variable, setup_entry) {
    if (!setup_constant || !setup_entry) {
      return null;
    }
    return {
      constant: setup_constant,
      action: setup_action || 'None',
      variable: setup_variable || 'None',
      entry: setup_entry,
    };
  }
  /**
   * Validate setup components
   * @param components The setup components to validate
   * @returns Validation result with errors if any
   */
  static validateComponents(components) {
    const errors = [];
    // Check required fields
    if (!components.constant) {
      errors.push('Constant element is required');
    }
    if (!components.entry) {
      errors.push('Entry method is required');
    }
    // Validate optional fields are not empty strings
    if (components.action === '') {
      components.action = 'None';
    }
    if (components.variable === '') {
      components.variable = 'None';
    }
    return {
      isValid: errors.length === 0,
      errors,
    };
  }
  /**
   * Create a default setup components object
   * @returns Default setup components
   */
  static createDefault() {
    return {
      constant: '',
      action: 'None',
      variable: 'None',
      entry: '',
    };
  }
  /**
   * Clone setup components
   * @param components The components to clone
   * @returns A deep copy of the components
   */
  static clone(components) {
    return {
      constant: components.constant,
      action: components.action || 'None',
      variable: components.variable || 'None',
      entry: components.entry,
    };
  }
}
//# sourceMappingURL=setupTransformer.js.map
