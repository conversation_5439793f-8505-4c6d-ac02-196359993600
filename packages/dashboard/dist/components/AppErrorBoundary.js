import { jsx as _jsx } from "react/jsx-runtime";
import { AppErrorBoundary as UnifiedAppErrorBoundary } from '@adhd-trading-dashboard/shared';
/**
 * App Error Boundary
 *
 * A top-level error boundary for the entire application.
 */
export const AppErrorBoundary = ({ children }) => {
    const handleError = (error) => {
        // Log the error to the console
        console.error('Application Error:', error);
        // Here you could also log to an error tracking service like Sentry
        // if (typeof window !== 'undefined' && window.Sentry) {
        //   window.Sentry.withScope((scope) => {
        //     scope.setTag('boundary', 'app');
        //     window.Sentry.captureException(error);
        //   });
        // }
    };
    return (_jsx(UnifiedAppErrorBoundary, { onError: handleError, name: "Application", children: children }));
};
export default AppErrorBoundary;
//# sourceMappingURL=AppErrorBoundary.js.map