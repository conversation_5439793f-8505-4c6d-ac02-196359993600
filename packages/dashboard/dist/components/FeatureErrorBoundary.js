import { jsx as _jsx } from 'react/jsx-runtime';
import { FeatureErrorBoundary as UnifiedFeatureErrorBoundary } from '@adhd-trading-dashboard/shared';
/**
 * Feature Error Boundary
 *
 * An error boundary for feature modules.
 */
export const FeatureErrorBoundary = ({ children, featureName, onError, onSkip }) => {
  const handleError = error => {
    console.error(`Error in feature "${featureName}":`, error);
    if (onError) {
      onError(error);
    }
    // Here you could also log to an error tracking service like Sentry
    // if (typeof window !== 'undefined' && window.Sentry) {
    //   window.Sentry.withScope((scope) => {
    //     scope.setTag('feature', featureName);
    //     scope.setTag('boundary', 'feature');
    //     window.Sentry.captureException(error);
    //   });
    // }
  };
  return _jsx(UnifiedFeatureErrorBoundary, {
    featureName: featureName,
    onError: handleError,
    onSkip: onSkip,
    children: children,
  });
};
export default FeatureErrorBoundary;
//# sourceMappingURL=FeatureErrorBoundary.js.map
