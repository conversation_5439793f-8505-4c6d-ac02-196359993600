/**
 * MoleculeSelector Component
 *
 * Three-tier dropdown component for selecting molecules in the atomic design system.
 * Structure: Atom → Type → Expression
 *
 * This component handles the molecule selection UI for the trading strategy atomic design system.
 */

import {
  TRADING_ATOMS,
  getExpressionsForAtomType,
  validateMoleculeForModel,
} from '@adhd-trading-dashboard/shared/constants/atomicDesign';
import type { ModelTemplate, MoleculeType } from '@adhd-trading-dashboard/shared/types/trading';
import React, { useCallback, useState } from 'react';
import styled from 'styled-components';

export interface SelectedMolecule {
  atom: string;
  type: MoleculeType;
  expression: string;
}

export interface MoleculeSelectorProps {
  /** Currently selected molecules */
  selectedMolecules: SelectedMolecule[];
  /** Callback when molecules change */
  onMoleculesChange: (molecules: SelectedMolecule[]) => void;
  /** Current model template for validation */
  modelTemplate?: ModelTemplate;
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const SelectorRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 2fr auto;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  align-items: end;
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing?.sm || '8px'};
  }
`;

const SelectGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const RemoveButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  background: ${({ theme }) => theme.colors?.error || '#dc3545'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${({ theme }) => theme.colors?.error || '#dc3545'}dd;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AddButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}dd;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ValidationMessage = styled.div<{ isError?: boolean }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  background: ${({ isError, theme }) =>
    isError
      ? (theme.colors?.error || '#dc3545') + '20'
      : (theme.colors?.warning || '#ffc107') + '20'};
  color: ${({ isError, theme }) =>
    isError ? theme.colors?.error || '#dc3545' : theme.colors?.warning || '#ffc107'};
  border: 1px solid
    ${({ isError, theme }) =>
      isError
        ? (theme.colors?.error || '#dc3545') + '40'
        : (theme.colors?.warning || '#ffc107') + '40'};
`;

/**
 * MoleculeSelector Component
 */
export const MoleculeSelector: React.FC<MoleculeSelectorProps> = ({
  selectedMolecules,
  onMoleculesChange,
  modelTemplate,
  disabled = false,
  className,
}) => {
  const [pendingMolecule, setPendingMolecule] = useState<Partial<SelectedMolecule>>({});

  /**
   * Handle atom selection
   */
  const handleAtomChange = useCallback((atom: string) => {
    setPendingMolecule({ atom, type: undefined, expression: undefined });
  }, []);

  /**
   * Handle type selection
   */
  const handleTypeChange = useCallback((type: MoleculeType) => {
    setPendingMolecule(prev => ({ ...prev, type, expression: undefined }));
  }, []);

  /**
   * Handle expression selection
   */
  const handleExpressionChange = useCallback((expression: string) => {
    setPendingMolecule(prev => ({ ...prev, expression }));
  }, []);

  /**
   * Add molecule to selection
   */
  const handleAddMolecule = useCallback(() => {
    if (pendingMolecule.atom && pendingMolecule.type && pendingMolecule.expression) {
      const newMolecule = pendingMolecule as SelectedMolecule;

      // Check for duplicates
      const isDuplicate = selectedMolecules.some(
        m =>
          m.atom === newMolecule.atom &&
          m.type === newMolecule.type &&
          m.expression === newMolecule.expression
      );

      if (!isDuplicate) {
        onMoleculesChange([...selectedMolecules, newMolecule]);
        setPendingMolecule({});
      }
    }
  }, [pendingMolecule, selectedMolecules, onMoleculesChange]);

  /**
   * Remove molecule from selection
   */
  const handleRemoveMolecule = useCallback(
    (index: number) => {
      const newMolecules = selectedMolecules.filter((_, i) => i !== index);
      onMoleculesChange(newMolecules);
    },
    [selectedMolecules, onMoleculesChange]
  );

  /**
   * Get available expressions for current atom and type
   */
  const getAvailableExpressions = useCallback(() => {
    if (!pendingMolecule.atom || !pendingMolecule.type) return [];
    return getExpressionsForAtomType(pendingMolecule.atom, pendingMolecule.type);
  }, [pendingMolecule.atom, pendingMolecule.type]);

  /**
   * Validate current molecule selection
   */
  const validateCurrentSelection = useCallback(() => {
    if (
      !modelTemplate ||
      !pendingMolecule.atom ||
      !pendingMolecule.type ||
      !pendingMolecule.expression
    ) {
      return null;
    }

    const isValid = validateMoleculeForModel(
      pendingMolecule.atom,
      pendingMolecule.type,
      pendingMolecule.expression,
      modelTemplate
    );

    return isValid ? null : `This molecule is not eligible for ${modelTemplate} model`;
  }, [pendingMolecule, modelTemplate]);

  const canAddMolecule = pendingMolecule.atom && pendingMolecule.type && pendingMolecule.expression;
  const validationError = validateCurrentSelection();

  return (
    <Container className={className}>
      {/* Selected Molecules */}
      {selectedMolecules.map((molecule, index) => (
        <SelectorRow key={index}>
          <SelectGroup>
            <Label>Atom</Label>
            <Select value={molecule.atom} disabled>
              <option value={molecule.atom}>{molecule.atom}</option>
            </Select>
          </SelectGroup>

          <SelectGroup>
            <Label>Type</Label>
            <Select value={molecule.type} disabled>
              <option value={molecule.type}>{molecule.type}</option>
            </Select>
          </SelectGroup>

          <SelectGroup>
            <Label>Expression</Label>
            <Select value={molecule.expression} disabled>
              <option value={molecule.expression}>{molecule.expression}</option>
            </Select>
          </SelectGroup>

          <RemoveButton
            onClick={() => handleRemoveMolecule(index)}
            disabled={disabled}
            title='Remove molecule'
          >
            ✕
          </RemoveButton>
        </SelectorRow>
      ))}

      {/* Add New Molecule */}
      <SelectorRow>
        <SelectGroup>
          <Label>Atom</Label>
          <Select
            value={pendingMolecule.atom || ''}
            onChange={e => handleAtomChange(e.target.value)}
            disabled={disabled}
          >
            <option value=''>Select Atom</option>
            {TRADING_ATOMS.map(atom => (
              <option key={atom.id} value={atom.name}>
                {atom.name}
              </option>
            ))}
          </Select>
        </SelectGroup>

        <SelectGroup>
          <Label>Type</Label>
          <Select
            value={pendingMolecule.type || ''}
            onChange={e => handleTypeChange(e.target.value as MoleculeType)}
            disabled={disabled || !pendingMolecule.atom}
          >
            <option value=''>Select Type</option>
            <option value='state'>State</option>
            <option value='behavior'>Behavior</option>
            <option value='target'>Target</option>
          </Select>
        </SelectGroup>

        <SelectGroup>
          <Label>Expression</Label>
          <Select
            value={pendingMolecule.expression || ''}
            onChange={e => handleExpressionChange(e.target.value)}
            disabled={disabled || !pendingMolecule.atom || !pendingMolecule.type}
          >
            <option value=''>Select Expression</option>
            {getAvailableExpressions().map(expression => (
              <option key={expression} value={expression}>
                {expression}
              </option>
            ))}
          </Select>
        </SelectGroup>

        <AddButton
          onClick={handleAddMolecule}
          disabled={disabled || !canAddMolecule || !!validationError}
          title='Add molecule'
        >
          Add
        </AddButton>
      </SelectorRow>

      {/* Validation Message */}
      {validationError && <ValidationMessage isError>{validationError}</ValidationMessage>}
    </Container>
  );
};

export default MoleculeSelector;
