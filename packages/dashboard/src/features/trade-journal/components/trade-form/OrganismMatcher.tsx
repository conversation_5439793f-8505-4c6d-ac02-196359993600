/**
 * OrganismMatcher Component
 *
 * Component that matches selected molecules against organism templates and suggests setup names.
 * Displays confidence levels and allows manual override of suggested setup names.
 */

import {
  getSuggestedSetupName,
  matchMoleculesToOrganisms,
} from '@adhd-trading-dashboard/shared/constants/atomicDesign';
import type { ModelTemplate, TradingOrganism } from '@adhd-trading-dashboard/shared/types/trading';
import React, { useCallback, useMemo } from 'react';
import styled from 'styled-components';

export interface SelectedMolecule {
  atom: string;
  type: string;
  expression: string;
}

export interface OrganismMatch {
  organism: TradingOrganism;
  confidence: 'exact' | 'partial' | 'none';
}

export interface OrganismMatcherProps {
  /** Currently selected molecules */
  selectedMolecules: SelectedMolecule[];
  /** Current suggested setup name */
  suggestedSetupName?: string;
  /** Manual setup name override */
  manualSetupName?: string;
  /** Callback when setup name changes */
  onSetupNameChange: (setupName: string, isManual: boolean) => void;
  /** Callback when model template changes */
  onModelTemplateChange: (modelTemplate: ModelTemplate) => void;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const MatchSection = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
`;

const SectionTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.md || '12px'} 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const MatchResult = styled.div<{ confidence: 'exact' | 'partial' | 'none' }>`
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  border: 1px solid;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};

  ${({ confidence, theme }) => {
    switch (confidence) {
      case 'exact':
        return `
          background: ${theme.colors?.success || '#28a745'}20;
          border-color: ${theme.colors?.success || '#28a745'}40;
          color: ${theme.colors?.success || '#28a745'};
        `;
      case 'partial':
        return `
          background: ${theme.colors?.warning || '#ffc107'}20;
          border-color: ${theme.colors?.warning || '#ffc107'}40;
          color: ${theme.colors?.warning || '#ffc107'};
        `;
      default:
        return `
          background: ${theme.colors?.textSecondary || 'var(--text-secondary)'}20;
          border-color: ${theme.colors?.textSecondary || 'var(--text-secondary)'}40;
          color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
        `;
    }
  }}
`;

const MatchTitle = styled.div`
  font-weight: 600;
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const MatchDetails = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  opacity: 0.8;
`;

const ConfidenceBadge = styled.span<{ confidence: 'exact' | 'partial' | 'none' }>`
  display: inline-block;
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-left: ${({ theme }) => theme.spacing?.sm || '8px'};

  ${({ confidence, theme }) => {
    switch (confidence) {
      case 'exact':
        return `
          background: ${theme.colors?.success || '#28a745'};
          color: white;
        `;
      case 'partial':
        return `
          background: ${theme.colors?.warning || '#ffc107'};
          color: white;
        `;
      default:
        return `
          background: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
          color: white;
        `;
    }
  }}
`;

const SetupNameSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border: 1px solid;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all 0.2s ease;

  ${({ variant, theme }) => {
    if (variant === 'primary') {
      return `
        background: ${theme.colors?.primary || 'var(--primary-color)'};
        border-color: ${theme.colors?.primary || 'var(--primary-color)'};
        color: white;

        &:hover:not(:disabled) {
          background: ${theme.colors?.primary || 'var(--primary-color)'}dd;
        }
      `;
    } else {
      return `
        background: transparent;
        border-color: ${theme.colors?.border || 'var(--border-primary)'};
        color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};

        &:hover:not(:disabled) {
          border-color: ${theme.colors?.primary || 'var(--primary-color)'};
          color: ${theme.colors?.primary || 'var(--primary-color)'};
        }
      `;
    }
  }}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

/**
 * OrganismMatcher Component
 */
export const OrganismMatcher: React.FC<OrganismMatcherProps> = ({
  selectedMolecules,
  suggestedSetupName,
  manualSetupName,
  onSetupNameChange,
  onModelTemplateChange,
  disabled = false,
  className,
}) => {
  /**
   * Get organism matches for current molecule selection
   */
  const organismMatches = useMemo(() => {
    if (selectedMolecules.length === 0) return [];
    return matchMoleculesToOrganisms(selectedMolecules);
  }, [selectedMolecules]);

  /**
   * Get suggested setup name
   */
  const suggestion = useMemo(() => {
    if (selectedMolecules.length === 0) return null;
    return getSuggestedSetupName(selectedMolecules);
  }, [selectedMolecules]);

  /**
   * Handle accepting suggested setup name
   */
  const handleAcceptSuggestion = useCallback(() => {
    if (suggestion) {
      onSetupNameChange(suggestion.name, false);
      if (suggestion.organism) {
        onModelTemplateChange(suggestion.organism.modelTemplate);
      }
    }
  }, [suggestion, onSetupNameChange, onModelTemplateChange]);

  /**
   * Handle manual setup name change
   */
  const handleManualNameChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onSetupNameChange(e.target.value, true);
    },
    [onSetupNameChange]
  );

  /**
   * Handle using organism setup name
   */
  const handleUseOrganismName = useCallback(
    (organism: TradingOrganism) => {
      onSetupNameChange(organism.name, false);
      onModelTemplateChange(organism.modelTemplate);
    },
    [onSetupNameChange, onModelTemplateChange]
  );

  return (
    <Container className={className}>
      {/* Organism Matches */}
      {organismMatches.length > 0 && (
        <MatchSection>
          <SectionTitle>Organism Matches</SectionTitle>
          {organismMatches.map((match, index) => (
            <MatchResult key={index} confidence={match.confidence}>
              <MatchTitle>
                {match.organism.name}
                <ConfidenceBadge confidence={match.confidence}>
                  {match.confidence} match
                </ConfidenceBadge>
              </MatchTitle>
              <MatchDetails>
                Model: {match.organism.modelTemplate} | Confidence:{' '}
                {match.organism.confidenceRating} | Molecules: {match.organism.molecules.length}
              </MatchDetails>
              {match.confidence !== 'none' && (
                <ButtonGroup style={{ marginTop: '8px' }}>
                  <Button
                    variant='primary'
                    onClick={() => handleUseOrganismName(match.organism)}
                    disabled={disabled}
                  >
                    Use This Setup
                  </Button>
                </ButtonGroup>
              )}
            </MatchResult>
          ))}
        </MatchSection>
      )}

      {/* Setup Name Configuration */}
      <MatchSection>
        <SectionTitle>Setup Name</SectionTitle>
        <SetupNameSection>
          {/* Suggested Name */}
          {suggestion && suggestion.confidence !== 'none' && (
            <InputGroup>
              <Label>Suggested Setup Name</Label>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Input value={suggestion.name} disabled style={{ flex: 1 }} />
                <ConfidenceBadge confidence={suggestion.confidence}>
                  {suggestion.confidence}
                </ConfidenceBadge>
                <Button variant='primary' onClick={handleAcceptSuggestion} disabled={disabled}>
                  Accept
                </Button>
              </div>
            </InputGroup>
          )}

          {/* Manual Override */}
          <InputGroup>
            <Label>Manual Setup Name</Label>
            <Input
              value={manualSetupName || ''}
              onChange={handleManualNameChange}
              placeholder='Enter custom setup name...'
              disabled={disabled}
            />
          </InputGroup>

          {/* Current Selection */}
          {(suggestedSetupName || manualSetupName) && (
            <InputGroup>
              <Label>Current Setup Name</Label>
              <Input
                value={manualSetupName || suggestedSetupName || ''}
                disabled
                style={{
                  background: manualSetupName
                    ? 'var(--warning-color, #ffc107)20'
                    : 'var(--success-color, #28a745)20',
                }}
              />
            </InputGroup>
          )}
        </SetupNameSection>
      </MatchSection>

      {/* No Matches Message */}
      {selectedMolecules.length > 0 && organismMatches.length === 0 && (
        <MatchSection>
          <SectionTitle>No Organism Matches</SectionTitle>
          <MatchResult confidence='none'>
            <MatchTitle>Custom Setup</MatchTitle>
            <MatchDetails>
              No predefined organisms match your molecule selection. Please enter a custom setup
              name above.
            </MatchDetails>
          </MatchResult>
        </MatchSection>
      )}
    </Container>
  );
};

export default OrganismMatcher;
