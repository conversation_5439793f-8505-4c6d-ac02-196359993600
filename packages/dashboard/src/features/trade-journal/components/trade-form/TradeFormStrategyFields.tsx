/**
 * Trade Form Strategy Fields Component
 *
 * REFACTORED: Simplified strategy section focusing on core strategy elements.
 * Removed duplicate pattern quality field and legacy setup dropdown.
 * Now uses modern SetupBuilder for setup construction.
 */

import React from 'react';
import styled from 'styled-components';
// Removed legacy setup imports - replaced by atomic design system
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { TradeFormValues } from '../../types';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const TextArea = styled.textarea`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-height: 100px;

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

interface TradeFormStrategyFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;
}

/**
 * Trade Form Strategy Fields Component
 */
const TradeFormStrategyFields: React.FC<TradeFormStrategyFieldsProps> = ({
  formValues,
  handleChange,
  // validationErrors, // Removed as unused
  setFormValues,
}) => {
  return (
    <>
      {/* Legacy setup construction and model selection removed */}
      {/* These are now handled by the Atomic Design Setup system */}

      {/* Notes Section - Moved to Analysis group via field config */}
      <SectionTitle>Session & Quality Assessment</SectionTitle>
      <FormGroup>
        <p
          style={{
            color: '#6b7280',
            fontSize: '0.875rem',
            margin: '0 0 16px 0',
            fontStyle: 'italic',
          }}
        >
          Setup construction and model selection have been moved to the Atomic Design Setup section
          below.
        </p>
      </FormGroup>
    </>
  );
};

export default TradeFormStrategyFields;
