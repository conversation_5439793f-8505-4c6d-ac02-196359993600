/**
 * AtomicDesignFieldGroup Component
 *
 * Specialized field group component for atomic design trading system.
 * Integrates molecule selection and organism matching functionality.
 */

import type { ModelTemplate } from '@adhd-trading-dashboard/shared/types/trading';
import React, { useCallback, useMemo } from 'react';
import styled from 'styled-components';
import type { TradeFormValues } from '../../types';
import { MoleculeSelector, type SelectedMolecule } from './MoleculeSelector';
import { OrganismMatcher } from './OrganismMatcher';

export interface AtomicDesignFieldGroupProps {
  /** Form values */
  formValues: TradeFormValues;
  /** Change handler for form values */
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  /** Whether the field group is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
`;

const Section = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const SectionTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};

  &::before {
    content: '';
    width: 4px;
    height: 24px;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    border-radius: 2px;
  }
`;

const InfoBox = styled.div`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.info || '#17a2b8'}20;
  border: 1px solid ${({ theme }) => theme.colors?.info || '#17a2b8'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.info || '#17a2b8'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  line-height: 1.5;
`;

/**
 * AtomicDesignFieldGroup Component
 */
export const AtomicDesignFieldGroup: React.FC<AtomicDesignFieldGroupProps> = ({
  formValues,
  handleChange,
  disabled = false,
  className,
}) => {
  /**
   * Get current molecules from form values
   */
  const currentMolecules = useMemo((): SelectedMolecule[] => {
    return formValues.atomicDesignMolecules || [];
  }, [formValues.atomicDesignMolecules]);

  /**
   * Handle molecules change
   */
  const handleMoleculesChange = useCallback(
    (molecules: SelectedMolecule[]) => {
      // Create synthetic event to update form values
      const syntheticEvent = {
        target: {
          name: 'atomicDesignMolecules',
          value: molecules,
        },
      } as React.ChangeEvent<HTMLInputElement>;

      handleChange(syntheticEvent);
    },
    [handleChange]
  );

  /**
   * Handle setup name change
   */
  const handleSetupNameChange = useCallback(
    (setupName: string, isManual: boolean) => {
      if (isManual) {
        // Update manual setup name
        const manualEvent = {
          target: {
            name: 'atomicDesignManualSetup',
            value: setupName,
          },
        } as React.ChangeEvent<HTMLInputElement>;
        handleChange(manualEvent);

        // Clear suggested setup name when manual is used
        const suggestedEvent = {
          target: {
            name: 'atomicDesignSuggestedSetup',
            value: '',
          },
        } as React.ChangeEvent<HTMLInputElement>;
        handleChange(suggestedEvent);
      } else {
        // Update suggested setup name
        const suggestedEvent = {
          target: {
            name: 'atomicDesignSuggestedSetup',
            value: setupName,
          },
        } as React.ChangeEvent<HTMLInputElement>;
        handleChange(suggestedEvent);

        // Clear manual setup name when suggested is used
        const manualEvent = {
          target: {
            name: 'atomicDesignManualSetup',
            value: '',
          },
        } as React.ChangeEvent<HTMLInputElement>;
        handleChange(manualEvent);
      }
    },
    [handleChange]
  );

  /**
   * Handle model template change
   */
  const handleModelTemplateChange = useCallback(
    (modelTemplate: ModelTemplate) => {
      const syntheticEvent = {
        target: {
          name: 'atomicDesignModelTemplate',
          value: modelTemplate,
        },
      } as React.ChangeEvent<HTMLInputElement>;

      handleChange(syntheticEvent);
    },
    [handleChange]
  );

  return (
    <Container className={className}>
      {/* Information Box */}
      <InfoBox>
        <strong>Atomic Design Trading System</strong>
        <br />
        Build your trading setup by selecting molecules (Atom + Type + Expression combinations). The
        system will automatically suggest matching organism templates based on your selections.
      </InfoBox>

      {/* Molecule Selection */}
      <Section>
        <SectionTitle>Molecule Selection</SectionTitle>
        <MoleculeSelector
          selectedMolecules={currentMolecules}
          onMoleculesChange={handleMoleculesChange}
          modelTemplate={formValues.atomicDesignModelTemplate}
          disabled={disabled}
        />
      </Section>

      {/* Organism Matching */}
      {currentMolecules.length > 0 && (
        <Section>
          <SectionTitle>Setup Recognition</SectionTitle>
          <OrganismMatcher
            selectedMolecules={currentMolecules}
            suggestedSetupName={formValues.atomicDesignSuggestedSetup}
            manualSetupName={formValues.atomicDesignManualSetup}
            onSetupNameChange={handleSetupNameChange}
            onModelTemplateChange={handleModelTemplateChange}
            disabled={disabled}
          />
        </Section>
      )}

      {/* Enhanced Setup Summary */}
      {(formValues.atomicDesignSuggestedSetup ||
        formValues.atomicDesignManualSetup ||
        currentMolecules.length > 0) && (
        <Section>
          <SectionTitle>Setup Summary</SectionTitle>
          <InfoBox>
            {formValues.atomicDesignManualSetup || formValues.atomicDesignSuggestedSetup ? (
              <>
                <strong>🎯 Selected Setup:</strong>{' '}
                {formValues.atomicDesignManualSetup || formValues.atomicDesignSuggestedSetup}
                <br />
                {formValues.atomicDesignModelTemplate && (
                  <>
                    <strong>🏎️ Model Template:</strong> {formValues.atomicDesignModelTemplate}
                    <br />
                  </>
                )}
                <strong>⚛️ Molecules:</strong> {currentMolecules.length} selected
                <br />
                <strong>📝 Source:</strong>{' '}
                {formValues.atomicDesignManualSetup ? 'Manual Override' : 'System Suggestion'}
                <br />
                {currentMolecules.length > 0 && (
                  <>
                    <strong>🧬 Molecule Details:</strong>
                    <br />
                    {currentMolecules.map((mol, idx) => (
                      <span
                        key={idx}
                        style={{
                          fontSize: '0.8rem',
                          opacity: 0.8,
                          display: 'block',
                          marginLeft: '16px',
                        }}
                      >
                        • {mol.atom} → {mol.type} → {mol.expression}
                      </span>
                    ))}
                  </>
                )}
              </>
            ) : (
              <>
                <strong>⚛️ Molecules Selected:</strong> {currentMolecules.length}
                <br />
                <em>
                  Select molecules above to get setup suggestions and model template detection.
                </em>
              </>
            )}
          </InfoBox>
        </Section>
      )}
    </Container>
  );
};

export default AtomicDesignFieldGroup;
