/**
 * TradeFormFieldGroups Component
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Organized field groups with F1 racing theme and clear hierarchy.
 *
 * BENEFITS:
 * - Organized field groups with clear sections
 * - F1 racing theme with consistent styling
 * - Responsive grid layout
 * - Reusable across different trading forms
 * - Better UX with logical field grouping
 */

import React from 'react';
import styled from 'styled-components';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { TradeFormValues } from '../../types';
import { AtomicDesignFieldGroup } from './AtomicDesignFieldGroup';
import { F1TradeFormField } from './F1TradeFormField';
import { FIELD_GROUPS, getFieldsByGroup, TradeFormFieldConfig } from './tradeFormFieldConfig';

export interface TradeFormFieldGroupsProps {
  /** Form values */
  formValues: TradeFormValues;
  /** Change handler */
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  /** Price change handler (triggers calculations) */
  handlePriceChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  /** Validation errors */
  validationErrors: ValidationErrors;
  /** Whether form is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const GroupsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
`;

const FieldGroup = styled.div<{ featured?: boolean }>`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  transition: all 0.2s ease;

  /* Enhanced styling for featured sections */
  ${({ featured, theme }) =>
    featured &&
    `
    border: 2px solid ${theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 12px 40px ${theme.colors?.primary || 'var(--primary-color)'}25;
    margin: ${theme.spacing?.xl || '32px'} 0;
    position: relative;
    background: linear-gradient(135deg,
      ${theme.colors?.surface || 'var(--bg-secondary)'} 0%,
      ${theme.colors?.primary || 'var(--primary-color)'}08 100%);

    &::after {
      content: '⭐ FEATURED';
      position: absolute;
      top: -1px;
      right: 16px;
      background: ${theme.colors?.primary || 'var(--primary-color)'};
      color: white;
      padding: 4px 12px;
      font-size: 0.75rem;
      font-weight: 700;
      border-radius: 0 0 6px 6px;
      letter-spacing: 0.05em;
      z-index: 2;
    }
  `}

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    ${({ featured, theme }) =>
      featured &&
      `
      box-shadow: 0 16px 50px ${theme.colors?.primary || 'var(--primary-color)'}35;
      transform: translateY(-2px);
    `}
  }
`;

const GroupHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;

const GroupTitleRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const GroupIcon = styled.div`
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
`;

const GroupTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const GroupDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;

const GroupContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const FieldsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing?.md || '12px'};
  }
`;

/**
 * TradeFormFieldGroups Component
 *
 * PATTERN: F1 Form Groups Pattern
 * - Racing-inspired section styling with icons
 * - Organized field groups with clear hierarchy
 * - Responsive grid layout for optimal UX
 * - Consistent F1 theme across all groups
 * - Accessible and keyboard navigable
 */
export const TradeFormFieldGroups: React.FC<TradeFormFieldGroupsProps> = ({
  formValues,
  handleChange,
  handlePriceChange,
  validationErrors,
  disabled = false,
  className,
}) => {
  /**
   * Determine which change handler to use based on field type
   */
  const getChangeHandler = (fieldConfig: TradeFormFieldConfig) => {
    // Use price change handler for fields that affect calculations
    if (['entryPrice', 'exitPrice', 'quantity'].includes(fieldConfig.name)) {
      return (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        handlePriceChange(e as React.ChangeEvent<HTMLInputElement>);
      };
    }
    return handleChange;
  };

  return (
    <GroupsContainer className={className}>
      {FIELD_GROUPS.map(group => {
        // Handle atomic design group specially with featured styling
        if (group.key === 'atomic') {
          return (
            <FieldGroup key={group.key} featured={true}>
              <GroupHeader>
                <GroupTitleRow>
                  <GroupIcon>{group.icon}</GroupIcon>
                  <div>
                    <GroupTitle>{group.title}</GroupTitle>
                    <GroupDescription>{group.description}</GroupDescription>
                  </div>
                </GroupTitleRow>
              </GroupHeader>

              <GroupContent>
                <AtomicDesignFieldGroup
                  formValues={formValues}
                  handleChange={handleChange}
                  disabled={disabled}
                />
              </GroupContent>
            </FieldGroup>
          );
        }

        // Handle regular field groups
        const fields = getFieldsByGroup(group.key);

        if (fields.length === 0) return null;

        return (
          <FieldGroup key={group.key}>
            <GroupHeader>
              <GroupTitleRow>
                <GroupIcon>{group.icon}</GroupIcon>
                <div>
                  <GroupTitle>{group.title}</GroupTitle>
                  <GroupDescription>{group.description}</GroupDescription>
                </div>
              </GroupTitleRow>
            </GroupHeader>

            <GroupContent>
              <FieldsGrid>
                {fields.map(fieldConfig => (
                  <F1TradeFormField
                    key={fieldConfig.name}
                    name={fieldConfig.name}
                    label={fieldConfig.label}
                    type={fieldConfig.type}
                    value={formValues[fieldConfig.name as keyof TradeFormValues]}
                    onChange={getChangeHandler(fieldConfig)}
                    options={fieldConfig.options}
                    inputProps={fieldConfig.inputProps}
                    error={validationErrors[fieldConfig.name]}
                    required={fieldConfig.required}
                    disabled={disabled}
                    placeholder={fieldConfig.placeholder}
                  />
                ))}
              </FieldsGrid>
            </GroupContent>
          </FieldGroup>
        );
      })}
    </GroupsContainer>
  );
};

export default TradeFormFieldGroups;
