/**
 * Trade Form Field Configuration
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Centralized configuration for all trading form fields.
 *
 * BENEFITS:
 * - Single source of truth for field definitions
 * - Easy to maintain and extend
 * - Consistent field options across forms
 * - Type-safe field configurations
 * - Reusable across different trading forms
 */

import { TradeFieldOption, TradeFieldType } from './F1TradeFormField';

export interface TradeFormFieldConfig {
  name: string;
  label: string;
  type: TradeFieldType;
  required?: boolean;
  options?: TradeFieldOption[];
  placeholder?: string;
  inputProps?: Record<string, any>;
  group: 'basic' | 'pricing' | 'strategy' | 'analysis' | 'atomic';
}

/**
 * Direction Options
 */
export const DIRECTION_OPTIONS: TradeFieldOption[] = [
  { value: 'long', label: 'Long' },
  { value: 'short', label: 'Short' },
];

/**
 * Result Options
 */
export const RESULT_OPTIONS: TradeFieldOption[] = [
  { value: 'win', label: 'Win' },
  { value: 'loss', label: 'Loss' },
  { value: 'breakeven', label: 'Breakeven' },
];

/**
 * Model Options
 */
export const MODEL_OPTIONS: TradeFieldOption[] = [
  { value: 'RD-Cont', label: 'RD-Cont' },
  { value: 'FVG-RD', label: 'FVG-RD' },
  { value: 'Combined', label: 'Combined' },
];

/**
 * Session Options
 */
export const SESSION_OPTIONS: TradeFieldOption[] = [
  { value: 'NY Open', label: 'NY Open' },
  { value: 'London Open', label: 'London Open' },
  { value: 'Lunch Macro', label: 'Lunch Macro' },
  { value: 'MOC', label: 'MOC' },
  { value: 'Overnight', label: 'Overnight' },
];

/**
 * RD Type Options
 */
export const RD_TYPE_OPTIONS: TradeFieldOption[] = [
  { value: 'Bullish', label: 'Bullish' },
  { value: 'Bearish', label: 'Bearish' },
  { value: 'Neutral', label: 'Neutral' },
];

/**
 * Draw on Liquidity Options
 */
export const DOL_STATUS_OPTIONS: TradeFieldOption[] = [
  { value: 'Hit', label: 'Hit' },
  { value: 'Missed', label: 'Missed' },
  { value: 'Partial', label: 'Partial' },
  { value: 'Pending', label: 'Pending' },
];

/**
 * Complete field configuration for trade forms
 */
export const TRADE_FORM_FIELDS: TradeFormFieldConfig[] = [
  // Basic Information Fields
  {
    name: 'date',
    label: 'Date',
    type: 'date',
    required: true,
    group: 'basic',
  },
  {
    name: 'symbol',
    label: 'Symbol',
    type: 'text',
    required: true,
    placeholder: 'e.g., AAPL, SPY, NQ',
    group: 'basic',
    inputProps: {
      style: { textTransform: 'uppercase' },
    },
  },
  {
    name: 'direction',
    label: 'Direction',
    type: 'select',
    required: true,
    options: DIRECTION_OPTIONS,
    group: 'basic',
  },
  {
    name: 'result',
    label: 'Result',
    type: 'select',
    required: true,
    options: RESULT_OPTIONS,
    group: 'basic',
  },

  // Pricing Fields
  {
    name: 'entryPrice',
    label: 'Entry Price',
    type: 'price',
    required: true,
    group: 'pricing',
  },
  {
    name: 'exitPrice',
    label: 'Exit Price',
    type: 'price',
    required: true,
    group: 'pricing',
  },
  {
    name: 'quantity',
    label: 'Quantity',
    type: 'quantity',
    required: true,
    group: 'pricing',
  },
  {
    name: 'profit',
    label: 'Profit/Loss ($)',
    type: 'price',
    required: true,
    group: 'pricing',
  },

  // Strategy Fields (Preserved)
  {
    name: 'session',
    label: 'Session',
    type: 'select',
    options: SESSION_OPTIONS,
    group: 'strategy',
  },
  {
    name: 'patternQuality',
    label: 'Pattern Quality (1-10)',
    type: 'number',
    group: 'strategy',
    inputProps: {
      min: 1,
      max: 10,
      step: 1,
    },
  },

  // Analysis Fields (Preserved - Notes only)
  {
    name: 'notes',
    label: 'Trade Notes',
    type: 'textarea',
    placeholder: 'Add notes about your trade analysis, market conditions, etc.',
    group: 'analysis',
  },
];

/**
 * Get fields by group
 */
export const getFieldsByGroup = (group: string): TradeFormFieldConfig[] => {
  return TRADE_FORM_FIELDS.filter(field => field.group === group);
};

/**
 * Get field configuration by name
 */
export const getFieldConfig = (name: string): TradeFormFieldConfig | undefined => {
  return TRADE_FORM_FIELDS.find(field => field.name === name);
};

/**
 * Field groups configuration
 */
export const FIELD_GROUPS = [
  {
    key: 'basic',
    title: 'Basic Information',
    description: 'Essential trade details and identification',
    icon: '📊',
  },
  {
    key: 'pricing',
    title: 'Pricing & P&L',
    description: 'Entry, exit prices and profit/loss calculations',
    icon: '💰',
  },
  {
    key: 'strategy',
    title: 'Session & Quality',
    description: 'Trading session and pattern quality assessment',
    icon: '🎯',
  },
  {
    key: 'analysis',
    title: 'Trade Analysis',
    description: 'Notes and additional trade analysis',
    icon: '🔍',
  },
  {
    key: 'atomic',
    title: 'Atomic Design Setup',
    description: 'Molecule selection and organism matching',
    icon: '⚛️',
  },
] as const;

/**
 * Validation rules for fields (cleaned up - legacy DOL/setup fields removed)
 */
export const FIELD_VALIDATION_RULES = {
  symbol: {
    pattern: /^[A-Z]{1,5}$/,
    message: 'Symbol must be 1-5 uppercase letters',
  },
  entryPrice: {
    min: 0.01,
    message: 'Entry price must be greater than 0',
  },
  exitPrice: {
    min: 0.01,
    message: 'Exit price must be greater than 0',
  },
  quantity: {
    min: 1,
    message: 'Quantity must be at least 1',
  },
  patternQuality: {
    min: 1,
    max: 10,
    message: 'Pattern quality must be between 1 and 10',
  },
  // Note: Atomic design fields have their own validation logic in components
} as const;
