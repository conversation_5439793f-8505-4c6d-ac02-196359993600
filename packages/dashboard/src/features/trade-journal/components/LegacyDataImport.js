import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import React, { useState, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';
// Simple icon components to replace lucide-react
const Upload = () => _jsx('span', { children: '\uD83D\uDCE4' });
const FileText = () => _jsx('span', { children: '\uD83D\uDCC4' });
const CheckCircle = () => _jsx('span', { children: '\u2705' });
const XCircle = () => _jsx('span', { children: '\u274C' });
const AlertCircle = () => _jsx('span', { children: '\u26A0\uFE0F' });
const Download = () => _jsx('span', { children: '\uD83D\uDCBE' });
const CSVImportTool = ({ onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [mappedData, setMappedData] = useState(null);
  const [importStatus, setImportStatus] = useState('idle'); // idle, processing, preview, imported
  const [stats, setStats] = useState(null);
  const [importMethod, setImportMethod] = useState('csv'); // csv, json, manual, paste
  // Updated column mappings for your specific CSV structure
  const COLUMN_MAPPINGS = {
    // Direct mappings for your exact column names
    date: ['date'],
    model_type: ['model type'],
    direction: ['direction'],
    market: ['market'],
    entry_price: ['entry price'],
    exit_price: ['exit price'],
    achieved_pl: ['achieved p/l'],
    r_multiple: ['r-multiple'],
    risk_points: ['risk (points)'],
    no_of_contracts: ['no. of contracts'],
    win_loss: ['win/loss'],
    pattern_quality_rating: ['pattern quality rating (1-5)'],
    session: ['session (time block)'],
    entry_time: ['entry time'],
    exit_time: ['exit time'],
    setup: ['setup'],
    primary_setup: ['primary setup'],
    secondary_setup: ['secondary setup'],
    notes: ['notes'],
    rd_type: ['rd type'],
    draw_on_liquidity: ['draw on liquidity'],
    fvg_date: ['fvg date'],
    entry_version: ['entry version'],
    liquidity_taken: ['liquidity taken'],
    additional_fvgs: ['additional fvgs'],
    dol: ['dol'],
    tradingview_link: ['tradingview link'],
    dol_target_type: ['dol target type'],
    beyond_target: ['beyond target'],
    clustering: ['clustering'],
    path_quality: ['path quality'],
    idr_context: ['idr context'],
    sequential_fvg_rd: ['sequential-fvg-rd'],
    dol_notes: ['dol notes'],
  };
  // Valid trading models from your schema
  const VALID_TRADING_MODELS = ['RD-Cont', 'FVG-RD', 'Combined'];
  // Valid sessions from your application
  const VALID_SESSIONS = [
    'NY Open',
    'Lunch Macro',
    'MOC',
    'London Open',
    'Asian Session',
    'Pre-Market',
    'After Hours',
    'NY AM',
    'NY PM',
  ];
  // Valid markets
  const VALID_MARKETS = ['MNQ', 'NQ', 'ES', 'MES', 'YM', 'MYM', 'RTY', 'M2K'];
  const handleFileUpload = useCallback(event => {
    const uploadedFile = event.target.files[0];
    if (uploadedFile && uploadedFile.type === 'text/csv') {
      setFile(uploadedFile);
      setImportStatus('processing');
      parseCSV(uploadedFile);
    }
  }, []);
  // Improved CSV parser that handles quoted fields properly
  const parseCSVLine = line => {
    const result = [];
    let current = '';
    let inQuotes = false;
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    result.push(current.trim());
    return result;
  };
  const parseCSV = async file => {
    try {
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());
      if (lines.length === 0) {
        console.error('CSV file is empty');
        setImportStatus('idle');
        return;
      }
      console.log('Total lines found:', lines.length);
      // FIXED: Skip the first row (category descriptions) and use the second row as headers
      const headerLine = lines[1]; // Row 2 contains actual headers
      const headers = parseCSVLine(headerLine).map(h => h.trim().toLowerCase().replace(/"/g, ''));
      console.log('Parsed headers:', headers);
      // Parse data rows starting from row 3 (index 2)
      const rows = lines
        .slice(2) // Skip first 2 rows (categories + headers)
        .filter(line => line.trim())
        .map(line => {
          const values = parseCSVLine(line);
          const row = {};
          headers.forEach((header, index) => {
            row[header] = values[index]?.trim().replace(/"/g, '') || '';
          });
          return row;
        });
      console.log('Parsed rows:', rows.length);
      console.log('Sample row:', rows[0]);
      setCsvData({ headers, rows });
      mapColumns(headers, rows);
    } catch (error) {
      console.error('CSV parsing error:', error);
      setImportStatus('idle');
    }
  };
  const mapColumns = (headers, rows) => {
    console.log('Starting column mapping...');
    console.log('Available headers:', headers);
    // Debug: Show which columns we can map
    const mappingResults = {};
    Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {
      const matchedHeader = headers.find(h =>
        possibleHeaders.some(ph => h.toLowerCase().includes(ph.toLowerCase()))
      );
      mappingResults[dbField] = matchedHeader || 'NOT FOUND';
    });
    console.log('Column mapping results:', mappingResults);
    const mapped = rows
      .map((row, rowIndex) => {
        const tradeRecord = {};
        // Smart mapping logic - map to TradeRecord schema
        Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {
          const matchedHeader = headers.find(h =>
            possibleHeaders.some(ph => h.toLowerCase().includes(ph.toLowerCase()))
          );
          if (matchedHeader && row[matchedHeader]) {
            let value = row[matchedHeader].trim();
            // Field-specific transformations to match TradeRecord schema
            if (dbField === 'model_type') {
              // Map to valid trading models - handle "RD-Cont." format
              let model = value.replace('.', ''); // Remove trailing period
              const validModel = VALID_TRADING_MODELS.find(
                vm =>
                  model.toLowerCase().includes(vm.toLowerCase()) ||
                  model
                    .toLowerCase()
                    .replace(/[-\s]/g, '')
                    .includes(vm.toLowerCase().replace(/[-\s]/g, ''))
              );
              tradeRecord[dbField] = validModel || 'Combined';
            } else if (dbField === 'direction') {
              // Standardize direction to 'Long' | 'Short'
              const lower = value.toLowerCase();
              if (lower.includes('long') || lower.includes('buy') || lower === 'l') {
                tradeRecord[dbField] = 'Long';
              } else if (lower.includes('short') || lower.includes('sell') || lower === 's') {
                tradeRecord[dbField] = 'Short';
              } else {
                tradeRecord[dbField] = value; // Keep original if already correct
              }
            } else if (dbField === 'win_loss') {
              // Standardize win/loss to 'Win' | 'Loss'
              const lower = value.toLowerCase();
              if (lower.includes('win') || lower.includes('profit') || lower === 'w') {
                tradeRecord[dbField] = 'Win';
              } else if (lower.includes('loss') || lower.includes('lose') || lower === 'l') {
                tradeRecord[dbField] = 'Loss';
              } else {
                tradeRecord[dbField] = value; // Keep original if already correct
              }
            } else if (dbField === 'session') {
              // Map to valid sessions
              const session = VALID_SESSIONS.find(
                vs =>
                  value.toLowerCase().includes(vs.toLowerCase()) ||
                  vs.toLowerCase().includes(value.toLowerCase())
              );
              tradeRecord[dbField] = session || value; // Keep original if no match
            } else if (dbField === 'market') {
              // Map to valid markets
              const market = VALID_MARKETS.find(vm =>
                value.toLowerCase().includes(vm.toLowerCase())
              );
              tradeRecord[dbField] = market || value; // Keep original if no exact match
            } else if (
              ['entry_price', 'exit_price', 'r_multiple', 'risk_points'].includes(dbField)
            ) {
              // FIXED: Clean up numeric fields - remove commas and other non-numeric chars
              const numericValue = parseFloat(value.replace(/[^-0-9.]/g, ''));
              tradeRecord[dbField] = isNaN(numericValue) ? null : numericValue;
            } else if (dbField === 'achieved_pl') {
              // FIXED: Handle P&L with dollar signs and commas
              const numericValue = parseFloat(value.replace(/[\$,]/g, ''));
              tradeRecord[dbField] = isNaN(numericValue) ? null : numericValue;
            } else if (dbField === 'pattern_quality_rating') {
              // Handle decimal quality ratings
              const rating = parseFloat(value) || 3;
              tradeRecord[dbField] = Math.max(1, Math.min(5, rating));
            } else if (dbField === 'no_of_contracts') {
              // Ensure contracts is a positive number
              const contracts = parseFloat(value) || 1;
              tradeRecord[dbField] = Math.max(0.1, contracts);
            } else if (['entry_time', 'exit_time'].includes(dbField)) {
              // Handle time fields - ensure proper format
              tradeRecord[dbField] = value.includes(':') ? value : null;
            } else {
              // String fields - just clean and assign
              tradeRecord[dbField] = value;
            }
          }
        });
        // Set required defaults for TradeRecord
        if (!tradeRecord.model_type) tradeRecord.model_type = 'Combined';
        if (!tradeRecord.direction) tradeRecord.direction = 'Long';
        if (!tradeRecord.market) tradeRecord.market = 'MNQ';
        if (!tradeRecord.pattern_quality_rating) tradeRecord.pattern_quality_rating = 3;
        if (!tradeRecord.no_of_contracts) tradeRecord.no_of_contracts = 1;
        // FIXED: Handle date format (MM/DD/YYYY to YYYY-MM-DD)
        if (tradeRecord.date) {
          try {
            const dateParts = tradeRecord.date.split('/');
            if (dateParts.length === 3) {
              const [month, day, year] = dateParts;
              tradeRecord.date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            }
          } catch (e) {
            tradeRecord.date = new Date().toISOString().split('T')[0];
          }
        } else {
          tradeRecord.date = new Date().toISOString().split('T')[0];
        }
        // Debug logging for first few rows
        if (rowIndex < 3) {
          console.log(`Row ${rowIndex} mapped:`, tradeRecord);
          console.log(
            `Has date: ${!!tradeRecord.date}, Has entry_price: ${!!tradeRecord.entry_price}, Has exit_price: ${!!tradeRecord.exit_price}`
          );
        }
        // More flexible validation - just need some price data
        const hasValidData =
          tradeRecord.entry_price || tradeRecord.exit_price || tradeRecord.achieved_pl;
        if (!hasValidData) {
          if (rowIndex < 3) console.log(`Row ${rowIndex} rejected: no price data`);
          return null;
        }
        return tradeRecord;
      })
      .filter(Boolean);
    setMappedData(mapped);
    // Generate comprehensive stats
    const validTrades = mapped.filter(
      t => t.date && t.model_type && (t.entry_price || t.exit_price)
    );
    const unmappedModels = mapped.filter(
      t => t.model_type && !VALID_TRADING_MODELS.includes(t.model_type)
    ).length;
    const missingPrices = mapped.filter(t => t.date && !t.entry_price && !t.exit_price).length;
    const winningTrades = mapped.filter(t => t.win_loss === 'Win').length;
    const losingTrades = mapped.filter(t => t.win_loss === 'Loss').length;
    setStats({
      totalRows: rows.length,
      validTrades: validTrades.length,
      unmappedModels,
      missingPrices,
      winningTrades,
      losingTrades,
      skipped: rows.length - validTrades.length,
      winRate:
        validTrades.length > 0
          ? ((winningTrades / (winningTrades + losingTrades)) * 100).toFixed(1)
          : 0,
    });
    setImportStatus('preview');
  };
  const handleImport = async () => {
    setImportStatus('processing');
    try {
      // Convert mapped data to CompleteTradeData and save to IndexedDB
      const importPromises = mappedData.map(async tradeRecord => {
        // The tradeRecord is already in the correct TradeRecord format from mapping
        const cleanedTradeRecord = {
          ...tradeRecord,
          // Ensure timestamps
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        // Create setup data from your CSV fields
        const setupData = {
          primary_setup: tradeRecord.primary_setup || null,
          secondary_setup: tradeRecord.secondary_setup || null,
          liquidity_taken: tradeRecord.liquidity_taken || null,
          additional_fvgs: tradeRecord.additional_fvgs || null,
          dol: tradeRecord.dol || null,
        };
        // Create FVG details from your CSV fields
        const fvgDetails = {
          fvg_date: tradeRecord.fvg_date || tradeRecord.date,
          rd_type: tradeRecord.rd_type || null,
          entry_version: tradeRecord.entry_version || null,
          draw_on_liquidity: tradeRecord.draw_on_liquidity || null,
        };
        // Create analysis data with your additional fields
        const analysisData = {
          tradingview_link: tradeRecord.tradingview_link || null,
          dol_target_type: tradeRecord.dol_target_type || null,
          beyond_target: tradeRecord.beyond_target || null,
          clustering: tradeRecord.clustering || null,
          path_quality: tradeRecord.path_quality || null,
          idr_context: tradeRecord.idr_context || null,
          sequential_fvg_rd: tradeRecord.sequential_fvg_rd || null,
          dol_notes:
            tradeRecord.dol_notes ||
            `Imported from CSV on ${new Date().toLocaleDateString()}. Original notes: ${
              tradeRecord.notes || 'None'
            }`,
        };
        // Create complete trade data structure matching your schema
        const completeTradeData = {
          trade: cleanedTradeRecord,
          fvg_details: fvgDetails,
          setup: setupData,
          analysis: analysisData,
        };
        return tradeStorageService.saveTradeWithDetails(completeTradeData);
      });
      await Promise.all(importPromises);
      setImportStatus('imported');
      // Call completion callback after successful import
      setTimeout(() => {
        onImportComplete?.();
      }, 2000); // Give user time to see success message
    } catch (error) {
      console.error('Import failed:', error);
      setImportStatus('preview'); // Return to preview on error
    }
  };
  const downloadCleanedData = () => {
    const csv = [
      // Headers
      Object.keys(mappedData[0] || {}).join(','),
      // Data rows
      ...mappedData.map(trade =>
        Object.values(trade)
          .map(v => `"${v}"`)
          .join(',')
      ),
    ].join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cleaned_trades.csv';
    a.click();
  };
  return _jsxs('div', {
    className: 'max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg',
    children: [
      _jsxs('div', {
        className: 'mb-6',
        children: [
          _jsx('h2', {
            className: 'text-2xl font-bold text-gray-900 mb-2',
            children: '\uD83D\uDCCA Legacy Trade Data Import',
          }),
          _jsx('p', {
            className: 'text-gray-600',
            children: 'Import your FVG Models trading data into the ADHD Trading Dashboard.',
          }),
          _jsxs('div', {
            className: 'mt-2 text-sm text-gray-500',
            children: [
              _jsx('strong', { children: 'Your CSV format detected:' }),
              ' FVG Models v3 with 40 columns including all FVG-specific fields',
            ],
          }),
        ],
      }),
      importStatus === 'idle' &&
        _jsxs('div', {
          className: 'border-2 border-dashed border-gray-300 rounded-lg p-8 text-center',
          children: [
            _jsx('div', { className: 'mx-auto text-4xl mb-4', children: _jsx(Upload, {}) }),
            _jsx('div', {
              className: 'mb-4',
              children: _jsxs('label', {
                htmlFor: 'csv-upload',
                className: 'cursor-pointer',
                children: [
                  _jsx('span', {
                    className: 'text-lg font-medium text-blue-600 hover:text-blue-500',
                    children: 'Upload Your FVG Models CSV',
                  }),
                  _jsx('input', {
                    id: 'csv-upload',
                    type: 'file',
                    accept: '.csv',
                    onChange: handleFileUpload,
                    className: 'sr-only',
                  }),
                ],
              }),
            }),
            _jsx('p', {
              className: 'text-sm text-gray-500',
              children: 'Select your "FVGModelsv3 Data Entry Form 2.csv" file',
            }),
          ],
        }),
      importStatus === 'processing' &&
        _jsxs('div', {
          className: 'text-center py-8',
          children: [
            _jsx('div', {
              className:
                'animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4',
            }),
            _jsx('p', {
              className: 'text-lg text-gray-600',
              children: 'Processing your FVG Models data...',
            }),
          ],
        }),
      importStatus === 'preview' &&
        stats &&
        _jsxs('div', {
          className: 'space-y-6',
          children: [
            _jsxs('div', {
              className: 'bg-gray-50 rounded-lg p-4',
              children: [
                _jsx('h3', { className: 'text-lg font-semibold mb-3', children: 'Import Summary' }),
                _jsxs('div', {
                  className: 'grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4',
                  children: [
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx(FileText, {}),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.totalRows, ' total rows'],
                        }),
                      ],
                    }),
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx(CheckCircle, {}),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.validTrades, ' valid trades'],
                        }),
                      ],
                    }),
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx(AlertCircle, {}),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.unmappedModels, ' unknown models'],
                        }),
                      ],
                    }),
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx(XCircle, {}),
                        _jsxs('span', { className: 'ml-2', children: [stats.skipped, ' skipped'] }),
                      ],
                    }),
                  ],
                }),
                _jsxs('div', {
                  className:
                    'grid grid-cols-2 md:grid-cols-4 gap-4 text-sm pt-2 border-t border-gray-200',
                  children: [
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx('span', { className: 'text-green-600', children: '\u2705' }),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.winningTrades, ' wins'],
                        }),
                      ],
                    }),
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx('span', { className: 'text-red-600', children: '\u274C' }),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.losingTrades, ' losses'],
                        }),
                      ],
                    }),
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx('span', { className: 'text-blue-600', children: '\uD83D\uDCCA' }),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.winRate, '% win rate'],
                        }),
                      ],
                    }),
                    _jsxs('div', {
                      className: 'flex items-center',
                      children: [
                        _jsx('span', { className: 'text-yellow-600', children: '\u26A0\uFE0F' }),
                        _jsxs('span', {
                          className: 'ml-2',
                          children: [stats.missingPrices, ' missing prices'],
                        }),
                      ],
                    }),
                  ],
                }),
              ],
            }),
            _jsxs('div', {
              className: 'bg-white border rounded-lg overflow-hidden',
              children: [
                _jsx('div', {
                  className: 'px-4 py-3 bg-gray-50 border-b',
                  children: _jsx('h3', {
                    className: 'text-lg font-semibold',
                    children: 'Preview (First 5 trades)',
                  }),
                }),
                _jsx('div', {
                  className: 'overflow-x-auto',
                  children: _jsxs('table', {
                    className: 'min-w-full divide-y divide-gray-200',
                    children: [
                      _jsx('thead', {
                        className: 'bg-gray-50',
                        children: _jsx('tr', {
                          children: Object.keys(mappedData[0] || {})
                            .slice(0, 10)
                            .map(key =>
                              _jsx(
                                'th',
                                {
                                  className:
                                    'px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase',
                                  children: key,
                                },
                                key
                              )
                            ),
                        }),
                      }),
                      _jsx('tbody', {
                        className: 'divide-y divide-gray-200',
                        children: mappedData.slice(0, 5).map((trade, index) =>
                          _jsx(
                            'tr',
                            {
                              className: 'hover:bg-gray-50',
                              children: Object.entries(trade)
                                .slice(0, 10)
                                .map(([field, value], i) =>
                                  _jsx(
                                    'td',
                                    {
                                      className: 'px-3 py-2 text-sm text-gray-900',
                                      children:
                                        typeof value === 'number' && value !== null
                                          ? value.toFixed(2)
                                          : value || '—',
                                    },
                                    i
                                  )
                                ),
                            },
                            index
                          )
                        ),
                      }),
                    ],
                  }),
                }),
              ],
            }),
            _jsxs('div', {
              className: 'flex gap-4',
              children: [
                _jsxs('button', {
                  onClick: handleImport,
                  className:
                    'flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium',
                  children: ['Import ', stats.validTrades, ' Trades'],
                }),
                _jsxs('button', {
                  onClick: downloadCleanedData,
                  className:
                    'bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-medium flex items-center gap-2',
                  children: [_jsx(Download, {}), 'Download Cleaned CSV'],
                }),
                _jsx('button', {
                  onClick: () => setImportStatus('idle'),
                  className:
                    'bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 font-medium',
                  children: 'Start Over',
                }),
              ],
            }),
          ],
        }),
      importStatus === 'imported' &&
        _jsxs('div', {
          className: 'text-center py-8',
          children: [
            _jsx('div', { className: 'mx-auto text-6xl mb-4', children: _jsx(CheckCircle, {}) }),
            _jsx('h3', {
              className: 'text-xl font-semibold text-gray-900 mb-2',
              children: 'Import Complete!',
            }),
            _jsxs('p', {
              className: 'text-gray-600 mb-6',
              children: [stats?.validTrades, ' trades have been imported into your dashboard'],
            }),
            _jsx('button', {
              onClick: () => setImportStatus('idle'),
              className: 'bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700',
              children: 'Import Another File',
            }),
          ],
        }),
    ],
  });
};
export default CSVImportTool;
//# sourceMappingURL=LegacyDataImport.js.map
