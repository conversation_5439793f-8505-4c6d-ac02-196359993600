/**
 * Trade Form Hook
 *
 * Custom hook for managing trade form state and logic
 */

import React, { useCallback, useState } from 'react';
// Removed unused imports - will be added back when needed for real data integration
// import { useNavigate } from 'react-router-dom'; // Removed as unused
// Removed unused imports - will be added back when needed for real data integration
// import { useTradeFormData } from './useTradeFormData'; // File removed
import { useTradeCalculations } from './useTradeCalculations';
import { useTradeSubmission } from './useTradeSubmission';
import { useTradeValidation } from './useTradeValidation';

// Options for dropdowns - Using actual trading models (not AI-generated generic ones)
export const MODEL_TYPE_OPTIONS = [
  { value: 'RD-Cont', label: 'RD-Cont' },
  { value: 'FVG-RD', label: 'FVG-RD' },
  { value: 'Combined', label: 'Combined' },
];

export const SESSION_OPTIONS = [
  { value: 'Pre-Market', label: 'Pre-Market' },
  { value: 'Regular Hours', label: 'Regular Hours' },
  { value: 'Power Hour', label: 'Power Hour' },
  { value: 'After Hours', label: 'After Hours' },
  { value: 'Overnight', label: 'Overnight' },
];

// SETUP_OPTIONS removed - using Setup Construction Matrix instead for modular setup building

export const MARKET_OPTIONS = [
  { value: 'Stocks', label: 'Stocks' },
  { value: 'Options', label: 'Options' },
  { value: 'Futures', label: 'Futures' },
  { value: 'Forex', label: 'Forex' },
  { value: 'Crypto', label: 'Crypto' },
  { value: 'Other', label: 'Other' },
];

export const ENTRY_VERSION_OPTIONS = [
  { value: 'First Entry', label: 'First Entry' },
  { value: 'Re-Entry', label: 'Re-Entry' },
  { value: 'Scale In', label: 'Scale In' },
  { value: 'Averaging Down', label: 'Averaging Down' },
  { value: 'Averaging Up', label: 'Averaging Up' },
];

export const PATTERN_QUALITY_OPTIONS = Array.from({ length: 10 }, (_, i) => ({
  value: String(i + 1),
  label: String(i + 1),
}));

/**
 * Main hook for managing trade form state and logic
 * @param tradeId The ID of the trade to load (optional)
 */
export const useTradeForm = (tradeId?: string) => {
  // const navigate = useNavigate(); // Removed as unused

  // Enhanced debugging for tradeId parameter and hash-based routing
  console.log(`useTradeForm hook initialized with tradeId: "${tradeId}"`);

  // For hash-based routing, check the hash part of the URL
  const currentPath = window.location.hash.substring(1); // Remove the leading #
  console.log(`Current hash path in useTradeForm: ${currentPath}`);

  // Extract trade ID from URL if not provided directly
  let effectiveTradeId = tradeId;
  if (!effectiveTradeId && currentPath.includes('/trade/edit/')) {
    // Extract ID from URL path for edit mode
    const matches = currentPath.match(/\/trade\/edit\/([^\/]+)/);
    if (matches && matches[1]) {
      effectiveTradeId = matches[1];
      console.log(`Extracted trade ID from URL: ${effectiveTradeId}`);
    }
  }

  // Determine if we're creating a new trade or editing an existing one
  const isNewTrade = effectiveTradeId === 'new' || currentPath.includes('/trade/new');
  const isEditMode =
    (effectiveTradeId && effectiveTradeId !== 'new') ||
    (currentPath.includes('/trade/edit/') && !currentPath.includes('/trade/edit/new'));

  console.log(`useTradeForm - isNewTrade: ${isNewTrade}, isEditMode: ${isEditMode}`);

  // Mock form state (replacing removed useTradeFormData) - RESTRUCTURED
  const [formValues, setFormValues] = useState<any>({
    // Basic fields
    date: '',
    symbol: '',
    direction: 'long',
    result: 'win',

    // Atomic design fields (FEATURED - replaces legacy setup construction)
    atomicDesignMolecules: [],
    atomicDesignSuggestedSetup: '',
    atomicDesignManualSetup: '',
    atomicDesignModelTemplate: undefined,

    // Timing fields (consolidated)
    entryDate: '',
    entryTime: '',
    exitTime: '',
    market: '',
    session: '',

    // Pricing fields
    entryPrice: '',
    exitPrice: '',
    quantity: '',
    profit: '',

    // Analysis fields (consolidated)
    patternQuality: '',
    notes: '',
  });
  const [isLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const tradeData = null; // Mock trade data

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setFormValues((prev: any) => ({
        ...prev,
        [name]: value,
      }));
    },
    []
  );

  // Use the validation hook to validate form data
  const { validationErrors, validateBasicInfoTab } = useTradeValidation();

  // Use the calculations hook to calculate trade metrics
  const { calculateProfitLoss } = useTradeCalculations(formValues, setFormValues);

  // Wrapper function for validateBasicInfoTab to pass the current form values
  const validateBasicInfo = () => validateBasicInfoTab(formValues);

  // Use the submission hook to handle form submission
  const { handleSubmit, isSubmitting } = useTradeSubmission(
    formValues,
    isEditMode,
    isNewTrade,
    tradeData as any, // Type assertion for interface compatibility
    validateBasicInfo,
    null, // No current tab validation needed
    null, // No active tab
    null, // No setActiveTab
    setError,
    setSuccess
  );

  return {
    formValues,
    setFormValues,
    handleChange,
    handleSubmit,
    isSubmitting,
    isLoading,
    error,
    success,
    validationErrors,
    isNewTrade,
    calculateProfitLoss,
  };
};
