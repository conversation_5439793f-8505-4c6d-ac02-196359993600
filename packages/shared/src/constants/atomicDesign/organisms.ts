/**
 * Trading Strategy Atomic Design - Organisms Library
 * 
 * This file contains the complete organism library for the trading strategy atomic design system.
 * Based on the Trading System Organism Templates from Notion.
 * 
 * Organisms represent complete, tradeable setups formed from combinations of molecules.
 */

import type { TradingOrganism, ModelTemplate } from '../../types/trading';

/**
 * Complete Organisms Library (7 Templates)
 */
export const TRADING_ORGANISMS: TradingOrganism[] = [
  {
    id: 'org001',
    name: 'NDOG Reaction Setup Targeting London Liquidity',
    modelTemplate: 'RD-Cont',
    modelAlias: 'Redelivery Continuation',
    molecules: [
      { atom: 'NDOG', type: 'state', expression: 'balanced_no_wick' },
      { atom: 'Strong-FVG', type: 'behavior', expression: 'formed' },
      { atom: 'London-H/L', type: 'target', expression: 'draw_on_liquidity' },
    ],
    biasAssistance: [
      {
        atom: '15m Top-FVG',
        timeframe: '15m',
        expression: 'balanced_no_redelivery',
        influence: 'strengthens setup confidence',
      },
    ],
    confidenceRating: 'high',
    validationStatus: 'untested',
    triggerCondition: 'All molecules must be active within the same session window',
  },
  {
    id: 'org002',
    name: 'Premarket Sweep Continuation Setup Targeting Daily High',
    modelTemplate: 'RD-Cont',
    modelAlias: 'Redelivery Continuation',
    molecules: [
      { atom: 'Premarket-H/L', type: 'behavior', expression: 'swept' },
      { atom: 'Strong-FVG', type: 'behavior', expression: 'formed' },
      { atom: 'Daily-H/L', type: 'target', expression: 'draw_on_liquidity' },
    ],
    biasAssistance: [
      {
        atom: 'Daily-Top/Bottom-FVG',
        expression: 'balanced',
        influence: 'provides directional bias',
      },
    ],
    confidenceRating: 'medium',
    validationStatus: 'untested',
    triggerCondition: 'Strong-FVG must form within 15m of sweep',
  },
  {
    id: 'org003',
    name: 'MNOR Redelivery Reversal Setup Targeting Premarket High',
    modelTemplate: 'FVG-RD',
    modelAlias: 'Fair Value Gap Redelivery',
    molecules: [
      { atom: 'MNOR-FPFVG', type: 'state', expression: 'balanced_no_redelivery' },
      { atom: 'MNOR-FPFVG', type: 'target', expression: 'expected_redelivery' },
      { atom: 'Premarket-H/L', type: 'target', expression: 'return_point' },
    ],
    biasAssistance: [
      {
        atom: 'Daily-Top/Bottom-FVG',
        expression: 'balanced',
        influence: 'confirms directional bias',
      },
    ],
    confidenceRating: 'high',
    validationStatus: 'untested',
    triggerCondition: 'MNOR must balance without wick, followed by a strong FVG',
  },
  {
    id: 'org004',
    name: 'Lunch Sweep Reversal Setup Targeting NDOG',
    modelTemplate: 'RD-Cont',
    modelAlias: 'Redelivery Continuation',
    molecules: [
      { atom: 'Lunch-H/L', type: 'behavior', expression: 'swept' },
      { atom: 'Strong-FVG', type: 'behavior', expression: 'formed' },
      { atom: 'NDOG', type: 'target', expression: 'draw_on_liquidity' },
    ],
    biasAssistance: [
      {
        atom: 'Daily-Top/Bottom-FVG',
        expression: 'balanced',
        influence: 'provides directional context',
      },
    ],
    confidenceRating: 'medium',
    validationStatus: 'untested',
    triggerCondition: 'Reversal after lunch sweep confirmed by FVG',
  },
  {
    id: 'org005',
    name: 'Premarket Reclaim Setup Targeting London High',
    modelTemplate: 'RD-Cont',
    modelAlias: 'Redelivery Continuation',
    molecules: [
      { atom: 'NWOG', type: 'behavior', expression: 'reclaimed' },
      { atom: 'Strong-FVG', type: 'behavior', expression: 'formed' },
      { atom: 'London-H/L', type: 'target', expression: 'draw_on_liquidity' },
    ],
    biasAssistance: [
      {
        atom: 'Daily-Top/Bottom-FVG',
        expression: 'balanced',
        influence: 'supports reclaim direction',
      },
    ],
    confidenceRating: 'medium',
    validationStatus: 'untested',
    triggerCondition: 'Return above NWOG after premarket sweep',
  },
  {
    id: 'org006',
    name: '09:30 Sweep Redelivery Setup Targeting Premarket High',
    modelTemplate: 'FVG-RD',
    modelAlias: 'Fair Value Gap Redelivery',
    molecules: [
      { atom: '09:30-OR-H/L', type: 'behavior', expression: 'swept' },
      { atom: 'Strong-FVG', type: 'behavior', expression: 'formed' },
      { atom: 'Premarket-H/L', type: 'target', expression: 'return_point' },
    ],
    biasAssistance: [
      {
        atom: 'MNOR-FPFVG',
        expression: 'balanced_no_wick',
        influence: 'provides context state',
      },
    ],
    confidenceRating: 'high',
    validationStatus: 'untested',
    triggerCondition: 'Strong-FVG forms off OR sweep after MNOR context',
  },
  {
    id: 'org007',
    name: 'RDRB-FVG Continuation Setup Targeting Weekly High',
    modelTemplate: 'RD-Cont',
    modelAlias: 'Redelivery Continuation',
    molecules: [
      { atom: 'RDRB-FVG', type: 'state', expression: 'balanced_no_wick' },
      { atom: 'RDRB-FVG', type: 'behavior', expression: 'tapped' },
      { atom: 'Weekly-H/L', type: 'target', expression: 'draw_on_liquidity' },
    ],
    biasAssistance: [
      {
        atom: 'NWOG',
        expression: 'unengaged',
        influence: 'supports higher timeframe target',
      },
    ],
    confidenceRating: 'high',
    validationStatus: 'untested',
    triggerCondition: 'Entry off RDRB-FVG + tap, aiming for HTF liquidity',
  },
];

/**
 * Get organism by ID
 */
export const getOrganismById = (id: string): TradingOrganism | undefined => {
  return TRADING_ORGANISMS.find(organism => organism.id === id);
};

/**
 * Get organisms by model template
 */
export const getOrganismsByModel = (model: ModelTemplate): TradingOrganism[] => {
  return TRADING_ORGANISMS.filter(organism => organism.modelTemplate === model);
};

/**
 * Get organisms by confidence rating
 */
export const getOrganismsByConfidence = (confidence: 'high' | 'medium' | 'low'): TradingOrganism[] => {
  return TRADING_ORGANISMS.filter(organism => organism.confidenceRating === confidence);
};

/**
 * Match molecules to organisms
 * Returns organisms that match the provided molecules with confidence level
 */
export const matchMoleculesToOrganisms = (
  selectedMolecules: Array<{ atom: string; type: string; expression: string }>
): Array<{ organism: TradingOrganism; confidence: 'exact' | 'partial' | 'none' }> => {
  const results: Array<{ organism: TradingOrganism; confidence: 'exact' | 'partial' | 'none' }> = [];

  for (const organism of TRADING_ORGANISMS) {
    const requiredMolecules = organism.molecules;
    let matchedCount = 0;

    // Check how many required molecules are matched
    for (const required of requiredMolecules) {
      const isMatched = selectedMolecules.some(
        selected =>
          selected.atom === required.atom &&
          selected.type === required.type &&
          selected.expression === required.expression
      );
      if (isMatched) {
        matchedCount++;
      }
    }

    // Determine confidence level
    let confidence: 'exact' | 'partial' | 'none';
    if (matchedCount === requiredMolecules.length && selectedMolecules.length === requiredMolecules.length) {
      confidence = 'exact';
    } else if (matchedCount > 0) {
      confidence = 'partial';
    } else {
      confidence = 'none';
    }

    // Only include organisms with some level of match
    if (confidence !== 'none') {
      results.push({ organism, confidence });
    }
  }

  // Sort by confidence (exact first, then partial)
  return results.sort((a, b) => {
    if (a.confidence === 'exact' && b.confidence !== 'exact') return -1;
    if (a.confidence !== 'exact' && b.confidence === 'exact') return 1;
    return 0;
  });
};

/**
 * Get suggested setup name based on molecule selection
 */
export const getSuggestedSetupName = (
  selectedMolecules: Array<{ atom: string; type: string; expression: string }>
): { name: string; confidence: 'exact' | 'partial' | 'none'; organism?: TradingOrganism } => {
  const matches = matchMoleculesToOrganisms(selectedMolecules);
  
  if (matches.length === 0) {
    return { name: 'Custom Setup', confidence: 'none' };
  }

  const bestMatch = matches[0];
  return {
    name: bestMatch.organism.name,
    confidence: bestMatch.confidence,
    organism: bestMatch.organism,
  };
};
