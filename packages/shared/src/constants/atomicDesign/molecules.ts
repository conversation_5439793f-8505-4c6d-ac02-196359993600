/**
 * Trading Strategy Atomic Design - Molecules Library
 * 
 * This file contains the complete molecule library for the trading strategy atomic design system.
 * Based on the Trading System Molecule Library from Notion.
 * 
 * Molecules are behavioral combinations of Atoms categorized into three types:
 * - State (conditions)
 * - Behavior (actions) 
 * - Target (objectives)
 */

import type { TradingMolecule, MoleculeType } from '../../types/trading';

/**
 * State Molecules (Conditions)
 * Describe the current condition or status of an atom
 */
const STATE_MOLECULES: TradingMolecule[] = [
  {
    id: 'mol1',
    atom: 'NDOG',
    type: 'state',
    expression: 'balanced_no_wick',
    description: 'NDOG balanced without wick revisit',
    modelEligibility: {
      'FVG-RD': false, // NDOG not valid for FVG-RD state/behavior
      'RD-Cont': true,
    },
  },
  {
    id: 'mol2',
    atom: 'MNOR-FPFVG',
    type: 'state',
    expression: 'balanced_no_redelivery',
    description: 'MNOR-FPFVG balanced without redelivery',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol3',
    atom: 'Strong-FVG',
    type: 'state',
    expression: 'untouched',
    description: 'Strong-FVG hasn\'t been interacted with',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol4',
    atom: 'NWOG',
    type: 'state',
    expression: 'unengaged',
    description: 'NWOG price hasn\'t reached the structure',
    modelEligibility: {
      'FVG-RD': false, // NWOG not valid for FVG-RD state/behavior
      'RD-Cont': true,
    },
  },
  {
    id: 'mol5',
    atom: 'London-H/L',
    type: 'state',
    expression: 'untaken',
    description: 'London High/Low liquidity level remains intact',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol6',
    atom: 'Daily-Top/Bottom-FVG',
    type: 'state',
    expression: 'balanced',
    description: 'Daily Top/Bottom FVG has been balanced/filled',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol7',
    atom: 'RDRB-FVG',
    type: 'state',
    expression: 'balanced_no_wick',
    description: 'RDRB-FVG balanced without wick revisit',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Behavior Molecules (Actions)
 * Describe actions or events happening to atoms
 */
const BEHAVIOR_MOLECULES: TradingMolecule[] = [
  {
    id: 'mol8',
    atom: 'London-H/L',
    type: 'behavior',
    expression: 'swept',
    description: 'London High/Low liquidity taken or structure cleared',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol9',
    atom: 'Strong-FVG',
    type: 'behavior',
    expression: 'formed',
    description: 'Strong-FVG new structure created',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol10',
    atom: 'RDRB-FVG',
    type: 'behavior',
    expression: 'tapped',
    description: 'RDRB-FVG structure touched/tested',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol11',
    atom: 'Premarket-H/L',
    type: 'behavior',
    expression: 'swept',
    description: 'Premarket High/Low liquidity taken',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol12',
    atom: '09:30-OR-H/L',
    type: 'behavior',
    expression: 'swept',
    description: '9:30 Opening Range High/Low swept',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol13',
    atom: 'NWOG',
    type: 'behavior',
    expression: 'reclaimed',
    description: 'NWOG structure regained after loss',
    modelEligibility: {
      'FVG-RD': false, // NWOG not valid for FVG-RD state/behavior
      'RD-Cont': true,
    },
  },
  {
    id: 'mol14',
    atom: 'Lunch-H/L',
    type: 'behavior',
    expression: 'swept',
    description: 'Lunch High/Low liquidity taken',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Target Molecules (Objectives)
 * Describe atoms acting as trade objectives
 */
const TARGET_MOLECULES: TradingMolecule[] = [
  {
    id: 'mol15',
    atom: 'London-H/L',
    type: 'target',
    expression: 'draw_on_liquidity',
    description: 'London High/Low targeted for liquidity',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol16',
    atom: 'MNOR-FPFVG',
    type: 'target',
    expression: 'expected_redelivery',
    description: 'MNOR-FPFVG anticipated for redelivery',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol17',
    atom: 'NDOG',
    type: 'target',
    expression: 'draw_on_liquidity',
    description: 'NDOG targeted for liquidity draw',
    modelEligibility: {
      'FVG-RD': false, // NDOG can be target but not state/behavior in FVG-RD
      'RD-Cont': true,
    },
  },
  {
    id: 'mol18',
    atom: 'Premarket-H/L',
    type: 'target',
    expression: 'return_point',
    description: 'Premarket High/Low expected for price return',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol19',
    atom: 'Daily-H/L',
    type: 'target',
    expression: 'draw_on_liquidity',
    description: 'Daily High/Low targeted for liquidity',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'mol20',
    atom: 'Weekly-H/L',
    type: 'target',
    expression: 'draw_on_liquidity',
    description: 'Weekly High/Low targeted for liquidity',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Complete Molecules Library (20 entries)
 */
export const TRADING_MOLECULES: TradingMolecule[] = [
  ...STATE_MOLECULES,
  ...BEHAVIOR_MOLECULES,
  ...TARGET_MOLECULES,
];

/**
 * Get molecules by type
 */
export const getMoleculesByType = (type: MoleculeType): TradingMolecule[] => {
  return TRADING_MOLECULES.filter(molecule => molecule.type === type);
};

/**
 * Get molecules by atom
 */
export const getMoleculesByAtom = (atom: string): TradingMolecule[] => {
  return TRADING_MOLECULES.filter(molecule => molecule.atom === atom);
};

/**
 * Get molecule by ID
 */
export const getMoleculeById = (id: string): TradingMolecule | undefined => {
  return TRADING_MOLECULES.find(molecule => molecule.id === id);
};

/**
 * Get molecules eligible for model
 */
export const getMoleculesForModel = (model: 'FVG-RD' | 'RD-Cont'): TradingMolecule[] => {
  return TRADING_MOLECULES.filter(molecule => molecule.modelEligibility[model]);
};

/**
 * Get expressions for atom and type combination
 */
export const getExpressionsForAtomType = (atom: string, type: MoleculeType): string[] => {
  return TRADING_MOLECULES
    .filter(molecule => molecule.atom === atom && molecule.type === type)
    .map(molecule => molecule.expression);
};

/**
 * Validate molecule combination for model eligibility
 */
export const validateMoleculeForModel = (
  atom: string, 
  type: MoleculeType, 
  expression: string, 
  model: 'FVG-RD' | 'RD-Cont'
): boolean => {
  const molecule = TRADING_MOLECULES.find(
    m => m.atom === atom && m.type === type && m.expression === expression
  );
  return molecule ? molecule.modelEligibility[model] : false;
};
