/**
 * Trading Strategy Atomic Design - Main Export
 * 
 * This file exports all atomic design constants and utilities for the trading strategy system.
 * 
 * Note: This is for trading strategy atomic design, not UI component atomic design.
 */

// Export all atoms
export {
  TRADING_ATOMS,
  getAtomsByCategory,
  getAtomById,
  getAtomsForModel,
} from './atoms';

// Export all molecules
export {
  TRADING_MOLECULES,
  getMoleculesByType,
  getMoleculesByAtom,
  getMoleculeById,
  getMoleculesForModel,
  getExpressionsForAtomType,
  validateMoleculeForModel,
} from './molecules';

// Export all organisms
export {
  TRADING_ORGANISMS,
  getOrganismById,
  getOrganismsByModel,
  getOrganismsByConfidence,
  matchMoleculesToOrganisms,
  getSuggestedSetupName,
} from './organisms';

// Re-export types for convenience
export type {
  TradingAtom,
  TradingMolecule,
  TradingOrganism,
  AtomCategory,
  MoleculeType,
  ModelTemplate,
  ConfidenceRating,
  ValidationStatus,
  BiasAssistance,
  AtomicDesignSelection,
} from '../../types/trading';
