/**
 * Trading Strategy Atomic Design - Atoms Library
 * 
 * This file contains the complete atom library for the trading strategy atomic design system.
 * Based on the Trading System Atomic Design Blueprint from Notion.
 * 
 * Note: This is for trading strategy atomic design, not UI component atomic design.
 */

import type { TradingAtom, AtomCategory } from '../../types/trading';

/**
 * Session-Based FVGs
 */
const SESSION_FVGS: TradingAtom[] = [
  {
    id: 'mnor-fpfvg',
    name: 'MNOR-FPFVG',
    category: 'session-fvg',
    description: 'Midnight New York Opening Range First Presented Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'am-fpfvg',
    name: 'AM-FPFVG',
    category: 'session-fvg',
    description: 'Asian Morning First Presented Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'pm-fpfvg',
    name: 'PM-FPFVG',
    category: 'session-fvg',
    description: 'Post Market First Presented Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'asia-fpfvg',
    name: 'Asia-FPFVG',
    category: 'session-fvg',
    description: 'Asian Session First Presented Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'premarket-fpfvg',
    name: 'Premarket-FPFVG',
    category: 'session-fvg',
    description: 'Premarket Session First Presented Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Higher Timeframe PD Arrays
 */
const HTF_PD_ARRAYS: TradingAtom[] = [
  {
    id: 'nwog',
    name: 'NWOG',
    category: 'htf-pd-array',
    description: 'New Week Opening Gap',
    modelEligibility: {
      'FVG-RD': false, // Too large for 1m redelivery mechanics
      'RD-Cont': true,
    },
  },
  {
    id: 'old-nwog',
    name: 'Old-NWOG',
    category: 'htf-pd-array',
    description: 'Previous Week Opening Gap',
    modelEligibility: {
      'FVG-RD': false,
      'RD-Cont': true,
    },
  },
  {
    id: 'ndog',
    name: 'NDOG',
    category: 'htf-pd-array',
    description: 'New Day Opening Gap',
    modelEligibility: {
      'FVG-RD': false, // Too large for 1m redelivery mechanics
      'RD-Cont': true,
    },
  },
  {
    id: 'old-ndog',
    name: 'Old-NDOG',
    category: 'htf-pd-array',
    description: 'Previous Day Opening Gap',
    modelEligibility: {
      'FVG-RD': false,
      'RD-Cont': true,
    },
  },
  {
    id: 'daily-top-bottom-fvg',
    name: 'Daily-Top/Bottom-FVG',
    category: 'htf-pd-array',
    description: 'Daily Timeframe Top or Bottom Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Strength-Based FVGs
 */
const STRENGTH_FVGS: TradingAtom[] = [
  {
    id: 'strong-fvg',
    name: 'Strong-FVG',
    category: 'strength-fvg',
    description: 'Strong Fair Value Gap with significant momentum',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'rdrb-fvg',
    name: 'RDRB-FVG',
    category: 'strength-fvg',
    description: 'Redelivery Rejection Block Fair Value Gap',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Liquidity Pools (Time-Derived)
 */
const LIQUIDITY_POOLS_TIME: TradingAtom[] = [
  {
    id: '0930-or-h',
    name: '09:30-OR-H',
    category: 'liquidity-pool-time',
    description: '9:30 Opening Range High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: '0930-or-l',
    name: '09:30-OR-L',
    category: 'liquidity-pool-time',
    description: '9:30 Opening Range Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'london-h',
    name: 'London-H',
    category: 'liquidity-pool-time',
    description: 'London Session High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'london-l',
    name: 'London-L',
    category: 'liquidity-pool-time',
    description: 'London Session Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'premarket-h',
    name: 'Premarket-H',
    category: 'liquidity-pool-time',
    description: 'Premarket Session High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'premarket-l',
    name: 'Premarket-L',
    category: 'liquidity-pool-time',
    description: 'Premarket Session Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'lunch-h',
    name: 'Lunch-H',
    category: 'liquidity-pool-time',
    description: 'Lunch Session High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'lunch-l',
    name: 'Lunch-L',
    category: 'liquidity-pool-time',
    description: 'Lunch Session Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Liquidity Pools (Higher Timeframe)
 */
const LIQUIDITY_POOLS_HTF: TradingAtom[] = [
  {
    id: 'monthly-h',
    name: 'Monthly-H',
    category: 'liquidity-pool-htf',
    description: 'Monthly High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'monthly-l',
    name: 'Monthly-L',
    category: 'liquidity-pool-htf',
    description: 'Monthly Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'weekly-h',
    name: 'Weekly-H',
    category: 'liquidity-pool-htf',
    description: 'Weekly High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'weekly-l',
    name: 'Weekly-L',
    category: 'liquidity-pool-htf',
    description: 'Weekly Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'daily-h',
    name: 'Daily-H',
    category: 'liquidity-pool-htf',
    description: 'Daily High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'daily-l',
    name: 'Daily-L',
    category: 'liquidity-pool-htf',
    description: 'Daily Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Historical PD Arrays
 */
const HISTORICAL_PD_ARRAYS: TradingAtom[] = [
  {
    id: 'prev-day-h',
    name: 'Prev-Day-H',
    category: 'historical-pd-array',
    description: 'Previous Day High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'prev-day-l',
    name: 'Prev-Day-L',
    category: 'historical-pd-array',
    description: 'Previous Day Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: '3day-h',
    name: '3Day-H',
    category: 'historical-pd-array',
    description: '3-Day High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: '3day-l',
    name: '3Day-L',
    category: 'historical-pd-array',
    description: '3-Day Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'prev-week-h',
    name: 'Prev-Week-H',
    category: 'historical-pd-array',
    description: 'Previous Week High',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
  {
    id: 'prev-week-l',
    name: 'Prev-Week-L',
    category: 'historical-pd-array',
    description: 'Previous Week Low',
    modelEligibility: {
      'FVG-RD': true,
      'RD-Cont': true,
    },
  },
];

/**
 * Complete Atoms Library
 */
export const TRADING_ATOMS: TradingAtom[] = [
  ...SESSION_FVGS,
  ...HTF_PD_ARRAYS,
  ...STRENGTH_FVGS,
  ...LIQUIDITY_POOLS_TIME,
  ...LIQUIDITY_POOLS_HTF,
  ...HISTORICAL_PD_ARRAYS,
];

/**
 * Get atoms by category
 */
export const getAtomsByCategory = (category: AtomCategory): TradingAtom[] => {
  return TRADING_ATOMS.filter(atom => atom.category === category);
};

/**
 * Get atom by ID
 */
export const getAtomById = (id: string): TradingAtom | undefined => {
  return TRADING_ATOMS.find(atom => atom.id === id);
};

/**
 * Get atoms eligible for model
 */
export const getAtomsForModel = (model: 'FVG-RD' | 'RD-Cont'): TradingAtom[] => {
  return TRADING_ATOMS.filter(atom => atom.modelEligibility[model]);
};
