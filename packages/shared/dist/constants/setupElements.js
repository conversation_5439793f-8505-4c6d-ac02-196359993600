/**
 * Setup Elements Constants
 *
 * Modular setup construction elements for the ADHD Trading Dashboard
 * These constants define the atomic elements that can be combined to create
 * infinite setup combinations without pre-defining every possibility.
 */
export const SETUP_ELEMENTS = {
  constant: {
    parentArrays: [
      'NWOG',
      'Old-NWOG',
      'NDOG',
      'Old-NDOG',
      'Monthly-FVG',
      'Weekly-FVG',
      'Daily-FVG',
      '15min-Top/Bottom-FVG',
      '1h-Top/Bottom-FVG',
    ],
    fvgTypes: [
      'Strong-FVG',
      'AM-FPFVG',
      'PM-FPFVG',
      'Asia-FPFVG',
      'Premarket-FPFVG',
      'MNOR-FVG',
      'Macro-FVG',
      'News-FVG',
      'Top/Bottom-FVG',
    ],
  },
  action: {
    liquidityEvents: [
      'None',
      'London-H/L',
      'Premarket-H/L',
      '09:30-Opening-Range-H/L',
      'Lunch-H/L',
      'Prev-Day-H/L',
      'Prev-Week-H/L',
      'Monthly-H/L',
      'Macro-H/L',
    ],
  },
  variable: {
    rdTypes: ['None', 'True-RD', 'IMM-RD', 'Dispersed-RD', 'Wide-Gap-RD'],
  },
  entry: {
    methods: ['Simple-Entry', 'Complex-Entry', 'Complex-Entry/Mini'],
  },
};
/**
 * Valid trading models (excluding invalid concepts)
 */
export const VALID_TRADING_MODELS = ['RD-Cont', 'FVG-RD', 'Combined'];
//# sourceMappingURL=setupElements.js.map
