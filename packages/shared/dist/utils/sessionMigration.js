/**
 * Session Migration Utilities
 *
 * Utilities for migrating from legacy session system to hierarchical system
 * while maintaining backward compatibility.
 */
import { SessionType, MacroPeriodType } from '../types/tradingSessions';
import { SessionUtils } from './sessionUtils';
/**
 * Legacy session mapping
 */
export const LEGACY_SESSION_MAPPING = {
  // Current legacy sessions from your codebase
  'NY Open': {
    session: SessionType.NEW_YORK_AM,
    description: 'New York market opening session',
  },
  'London Open': {
    session: SessionType.LONDON,
    description: 'London market opening session',
  },
  'Lunch Macro': {
    macro: MacroPeriodType.LUNCH_MACRO,
    description: 'Lunch time macro period',
  },
  MOC: {
    macro: MacroPeriodType.MOC,
    description: 'Market on Close period',
  },
  Overnight: {
    session: SessionType.OVERNIGHT,
    description: 'Overnight trading session',
  },
  // Database sessions from your existing schema
  'Pre-Market': {
    session: SessionType.PRE_MARKET,
    description: 'Pre-market trading hours',
  },
  '10:50-11:10': {
    macro: MacroPeriodType.MID_MORNING_REVERSION,
    description: 'Mid-morning macro window',
  },
  '11:50-12:10': {
    macro: MacroPeriodType.PRE_LUNCH,
    description: 'Pre-lunch macro window (sub-period of extended lunch)',
  },
  '11:30-13:30': {
    macro: MacroPeriodType.LUNCH_MACRO_EXTENDED,
    description: 'Extended lunch macro spanning both AM and PM sessions',
  },
  '13:50-14:10': {
    macro: MacroPeriodType.POST_LUNCH,
    description: 'Post-lunch macro window',
  },
  '14:50-15:10': {
    macro: MacroPeriodType.PRE_CLOSE,
    description: 'Pre-close macro window',
  },
  '15:15-15:45': {
    macro: MacroPeriodType.POWER_HOUR,
    description: 'Late afternoon window (Power Hour)',
  },
  'Post MOC': {
    session: SessionType.AFTER_HOURS,
    description: 'After-hours trading',
  },
  // Additional common variations
  'Power Hour': {
    macro: MacroPeriodType.POWER_HOUR,
    description: 'Power hour trading period',
  },
  'After Hours': {
    session: SessionType.AFTER_HOURS,
    description: 'After-hours trading session',
  },
  'Pre Market': {
    session: SessionType.PRE_MARKET,
    description: 'Pre-market trading session',
  },
};
/**
 * Session Migration Utilities
 */
export class SessionMigrationUtils {
  /**
   * Convert legacy session string to new SessionSelection
   */
  static migrateLegacySession(legacySession) {
    const mapping = LEGACY_SESSION_MAPPING[legacySession];
    if (!mapping) {
      console.warn(`Unknown legacy session: ${legacySession}`);
      return null;
    }
    return SessionUtils.createSessionSelection(mapping.session, mapping.macro);
  }
  /**
   * Convert SessionSelection back to legacy format for backward compatibility
   */
  static toLegacySession(selection) {
    if (selection.selectionType === 'macro' && selection.macroPeriod) {
      // Find the legacy name for this macro
      for (const [legacyName, mapping] of Object.entries(LEGACY_SESSION_MAPPING)) {
        if (mapping.macro === selection.macroPeriod) {
          return legacyName;
        }
      }
      // Fallback to display label
      return selection.displayLabel;
    }
    if (selection.selectionType === 'session' && selection.session) {
      // Find the legacy name for this session
      for (const [legacyName, mapping] of Object.entries(LEGACY_SESSION_MAPPING)) {
        if (mapping.session === selection.session) {
          return legacyName;
        }
      }
      // Fallback to display label
      return selection.displayLabel;
    }
    if (selection.selectionType === 'custom' && selection.customTimeRange) {
      return `${selection.customTimeRange.start}-${selection.customTimeRange.end}`;
    }
    return selection.displayLabel;
  }
  /**
   * Migrate database session records to new format
   */
  static migrateDatabaseSessions(dbSessions) {
    return dbSessions.map(session => {
      const mapping = LEGACY_SESSION_MAPPING[session.name];
      return {
        ...session,
        session_type: mapping?.session,
        macro_type: mapping?.macro,
        migrated: !!mapping,
      };
    });
  }
  /**
   * Generate migration report
   */
  static generateMigrationReport(legacySessions) {
    const mappings = legacySessions.map(legacy => {
      const migrated = this.migrateLegacySession(legacy);
      return {
        legacy,
        new: migrated,
        status: migrated ? 'success' : 'failed',
      };
    });
    const migrated = mappings.filter(m => m.status === 'success').length;
    const unmigrated = mappings.filter(m => m.status === 'failed').map(m => m.legacy);
    return {
      total: legacySessions.length,
      migrated,
      unmigrated,
      mappings,
    };
  }
  /**
   * Get all available legacy session names for backward compatibility
   */
  static getLegacySessionNames() {
    return Object.keys(LEGACY_SESSION_MAPPING);
  }
  /**
   * Check if a legacy session name is valid
   */
  static isValidLegacySession(sessionName) {
    return sessionName in LEGACY_SESSION_MAPPING;
  }
  /**
   * Get suggested new sessions for unmapped legacy sessions
   */
  static getSuggestedMappings(unmappedSessions) {
    return unmappedSessions.map(legacy => {
      const suggestions = [];
      // Time-based suggestions
      const timeMatch = legacy.match(/(\d{1,2}):?(\d{2})-(\d{1,2}):?(\d{2})/);
      if (timeMatch) {
        const [, startHour, startMin] = timeMatch; // endHour, endMin available but not used in current implementation
        const startTime = `${startHour.padStart(2, '0')}:${startMin}:00`;
        // const endTime = `${endHour.padStart(2, '0')}:${endMin}:00`; // Not used in current implementation
        const validation = SessionUtils.validateTime(startTime);
        if (validation.suggestedMacro) {
          suggestions.push({
            macro: validation.suggestedMacro,
            confidence: 0.9,
            reason: 'Time range matches existing macro period',
          });
        } else if (validation.suggestedSession) {
          suggestions.push({
            session: validation.suggestedSession,
            confidence: 0.7,
            reason: 'Time range falls within session',
          });
        }
      }
      // Keyword-based suggestions
      const lowerLegacy = legacy.toLowerCase();
      if (lowerLegacy.includes('open')) {
        if (lowerLegacy.includes('london')) {
          suggestions.push({
            session: SessionType.LONDON,
            confidence: 0.8,
            reason: 'Contains "London" and "open" keywords',
          });
        } else {
          suggestions.push({
            session: SessionType.NEW_YORK_AM,
            confidence: 0.7,
            reason: 'Contains "open" keyword',
          });
        }
      }
      if (lowerLegacy.includes('close') || lowerLegacy.includes('moc')) {
        suggestions.push({
          macro: MacroPeriodType.MOC,
          confidence: 0.8,
          reason: 'Contains close-related keywords',
        });
      }
      if (lowerLegacy.includes('lunch')) {
        suggestions.push({
          macro: MacroPeriodType.LUNCH_MACRO,
          confidence: 0.8,
          reason: 'Contains "lunch" keyword',
        });
      }
      if (lowerLegacy.includes('power') || lowerLegacy.includes('hour')) {
        suggestions.push({
          macro: MacroPeriodType.POWER_HOUR,
          confidence: 0.7,
          reason: 'Contains power hour keywords',
        });
      }
      return {
        legacy,
        suggestions: suggestions.sort((a, b) => b.confidence - a.confidence),
      };
    });
  }
}
//# sourceMappingURL=sessionMigration.js.map
