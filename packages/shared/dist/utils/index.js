/**
 * Utilities
 *
 * Common utility functions for the application
 */
/**
 * Format currency value
 * @param value - The value to format
 * @param currency - The currency symbol (default: $)
 * @returns Formatted currency string
 */
export function formatCurrency(value, currency = '$') {
  return `${currency}${value.toFixed(2)}`;
}
/**
 * Format percentage value
 * @param value - The value to format (0-1)
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export function formatPercentage(value, decimals = 1) {
  return `${(value * 100).toFixed(decimals)}%`;
}
/**
 * Format date
 * @param date - Date string or Date object
 * @param format - Format style (default: 'short')
 * @returns Formatted date string
 */
export function formatDate(date, format = 'short') {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  switch (format) {
    case 'medium':
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    case 'long':
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    case 'short':
    default:
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
  }
}
/**
 * Truncate text
 * @param text - The text to truncate
 * @param maxLength - Maximum length (default: 50)
 * @returns Truncated text
 */
export function truncateText(text, maxLength = 50) {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength - 3)}...`;
}
/**
 * Generate a unique ID
 * @returns Unique ID string
 */
export function generateId() {
  return Math.random().toString(36).substring(2, 9);
}
/**
 * Debounce function
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce(func, wait) {
  let timeout = null;
  return function (...args) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
/**
 * Throttle function
 * @param func - Function to throttle
 * @param limit - Limit time in milliseconds
 * @returns Throttled function
 */
export function throttle(func, limit) {
  let inThrottle = false;
  return function (...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}
// Export session utilities
export { SessionUtils } from './sessionUtils';
// Export timezone utilities (excluding component types to avoid conflicts)
export {
  getCurrentNYTime,
  getCurrentNYMinutes,
  convertNYToLocal,
  convertLocalToNY,
  getUserTimezone,
  formatTimeForMobile,
  formatTimeForDesktop,
  formatTimeInterval,
  getTimeUntilNYTime,
  getCurrentDualTime,
  convertSessionToDualTime,
  getSessionStatus,
  timeToMinutes,
  minutesToTime,
  isCurrentTimeInNYWindow,
  // Note: DualTimeDisplay type excluded to avoid conflict with component export
} from './timeZoneUtils';
//# sourceMappingURL=index.js.map
