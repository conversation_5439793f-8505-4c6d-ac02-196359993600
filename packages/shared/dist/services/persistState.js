/**
 * Create a persisted reducer
 *
 * @param reducer - The reducer function
 * @param options - The persist state options
 * @returns The persisted reducer and initial state
 */
export function persistState(reducer, options) {
  const {
    key,
    initialState,
    version = 1,
    migrate,
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    filter = state => state,
    merge = (persistedState, initialState) => ({ ...initialState, ...persistedState }),
    debug = false,
  } = options;
  // Load persisted state from local storage
  const loadState = () => {
    try {
      const serializedState = localStorage.getItem(key);
      if (serializedState === null) {
        return null;
      }
      const { state, version: persistedVersion } = deserialize(serializedState);
      // Migrate state if version has changed
      if (persistedVersion !== version && migrate) {
        if (debug) {
          console.log(`Migrating state from version ${persistedVersion} to ${version}`);
        }
        return migrate(state, persistedVersion);
      }
      return state;
    } catch (err) {
      if (debug) {
        console.error('Error loading state from local storage:', err);
      }
      return null;
    }
  };
  // Save state to local storage
  const saveState = state => {
    try {
      const filteredState = filter(state);
      const serializedState = serialize({
        state: filteredState,
        version,
      });
      localStorage.setItem(key, serializedState);
    } catch (err) {
      if (debug) {
        console.error('Error saving state to local storage:', err);
      }
    }
  };
  // Clear persisted state
  const clear = () => {
    try {
      localStorage.removeItem(key);
    } catch (err) {
      if (debug) {
        console.error('Error clearing state from local storage:', err);
      }
    }
  };
  // Load persisted state and merge with initial state
  const persistedState = loadState();
  const mergedInitialState = persistedState ? merge(persistedState, initialState) : initialState;
  if (debug && persistedState) {
    console.log('Loaded persisted state:', persistedState);
    console.log('Merged initial state:', mergedInitialState);
  }
  // Create persisted reducer
  const persistedReducer = (state, action) => {
    const newState = reducer(state, action);
    saveState(newState);
    return newState;
  };
  return {
    reducer: persistedReducer,
    initialState: mergedInitialState,
    clear,
  };
}
//# sourceMappingURL=persistState.js.map
