/**
 * usePagination Hook
 *
 * A hook for handling pagination state and calculations.
 */
import { useState, useMemo, useCallback } from 'react';
import { useLocalStorage } from './useLocalStorage';
/**
 * usePagination Hook
 *
 * @param options - Pagination options
 * @returns Pagination state and actions
 */
export function usePagination(options) {
  const {
    totalItems,
    itemsPerPage: initialItemsPerPage = 10,
    initialPage = 1,
    persistKey,
  } = options;
  // Use localStorage if persistKey is provided
  const [currentPage, setCurrentPage] = persistKey
    ? useLocalStorage(`${persistKey}_page`, initialPage)
    : useState(initialPage);
  const [itemsPerPage, setItemsPerPageState] = persistKey
    ? useLocalStorage(`${persistKey}_itemsPerPage`, initialItemsPerPage)
    : useState(initialItemsPerPage);
  // Calculate total pages
  const totalPages = useMemo(
    () => Math.max(1, Math.ceil(totalItems / itemsPerPage)),
    [totalItems, itemsPerPage]
  );
  // Ensure current page is within valid range
  const validatedCurrentPage = useMemo(() => {
    return Math.min(Math.max(1, currentPage), totalPages);
  }, [currentPage, totalPages]);
  // If validatedCurrentPage is different from currentPage, update it
  if (validatedCurrentPage !== currentPage) {
    setCurrentPage(validatedCurrentPage);
  }
  // Calculate pagination state
  const startIndex = (validatedCurrentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage - 1, totalItems - 1);
  const hasPreviousPage = validatedCurrentPage > 1;
  const hasNextPage = validatedCurrentPage < totalPages;
  // Generate page range (for pagination UI)
  const pageRange = useMemo(() => {
    const maxPagesToShow = 5;
    const pages = [];
    if (totalPages <= maxPagesToShow) {
      // Show all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show a subset of pages
      let startPage = Math.max(1, validatedCurrentPage - Math.floor(maxPagesToShow / 2));
      const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
      // Adjust startPage if endPage is at the limit
      if (endPage === totalPages) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }
    return pages;
  }, [validatedCurrentPage, totalPages]);
  // Pagination actions
  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setCurrentPage(validatedCurrentPage + 1);
    }
  }, [hasNextPage, validatedCurrentPage, setCurrentPage]);
  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setCurrentPage(validatedCurrentPage - 1);
    }
  }, [hasPreviousPage, validatedCurrentPage, setCurrentPage]);
  const goToPage = useCallback(
    page => {
      const targetPage = Math.min(Math.max(1, page), totalPages);
      setCurrentPage(targetPage);
    },
    [totalPages, setCurrentPage]
  );
  const setItemsPerPage = useCallback(
    newItemsPerPage => {
      setItemsPerPageState(newItemsPerPage);
      // Reset to first page when changing items per page
      setCurrentPage(1);
    },
    [setItemsPerPageState, setCurrentPage]
  );
  return {
    currentPage: validatedCurrentPage,
    itemsPerPage,
    totalPages,
    hasPreviousPage,
    hasNextPage,
    startIndex,
    endIndex,
    pageRange,
    nextPage,
    previousPage,
    goToPage,
    setItemsPerPage,
  };
}
//# sourceMappingURL=usePagination.js.map
