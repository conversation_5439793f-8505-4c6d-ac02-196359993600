/**
 * useDataFormatting Hook
 *
 * EXTRACTED FROM: Multiple components with formatting logic
 * Centralizes data formatting functions for consistent display across components.
 *
 * BENEFITS:
 * - Consistent formatting across the app
 * - Locale-aware formatting
 * - Customizable formatting options
 * - Reduced duplication of formatting logic
 */
import { useMemo } from 'react';
/**
 * Custom hook for data formatting with consistent options
 *
 * @param defaultLocale - Default locale for formatting (default: 'en-US')
 * @returns Object with formatting functions
 *
 * @example
 * ```typescript
 * const { formatCurrency, formatPercent, formatDate } = useDataFormatting();
 *
 * const formattedPrice = formatCurrency(1234.56); // "$1,234.56"
 * const formattedPercent = formatPercent(75.5); // "75.50%"
 * const formattedDate = formatDate(new Date(), 'short'); // "12/25/2023"
 * ```
 */
export const useDataFormatting = (defaultLocale = 'en-US') => {
  const formatters = useMemo(() => {
    /**
     * Formats a number as currency
     */
    const formatCurrency = (value, options = {}) => {
      const {
        currency = 'USD',
        locale = defaultLocale,
        minimumFractionDigits = 2,
        maximumFractionDigits = 2,
        showPositiveSign = false,
      } = options;
      const formatter = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
        minimumFractionDigits,
        maximumFractionDigits,
      });
      const formatted = formatter.format(Math.abs(value));
      if (value > 0 && showPositiveSign) {
        return `+${formatted}`;
      } else if (value < 0) {
        return `-${formatted}`;
      }
      return formatted;
    };
    /**
     * Formats a number as percentage
     */
    const formatPercent = (value, options = {}) => {
      const {
        locale = defaultLocale,
        minimumFractionDigits = 2,
        maximumFractionDigits = 2,
        showPositiveSign = false,
      } = options;
      const formatter = new Intl.NumberFormat(locale, {
        style: 'percent',
        minimumFractionDigits,
        maximumFractionDigits,
      });
      // Convert to decimal if value appears to be a percentage (> 1)
      const decimalValue = value > 1 ? value / 100 : value;
      const formatted = formatter.format(Math.abs(decimalValue));
      if (decimalValue > 0 && showPositiveSign) {
        return `+${formatted}`;
      } else if (decimalValue < 0) {
        return `-${formatted}`;
      }
      return formatted;
    };
    /**
     * Formats a number with locale-specific grouping
     */
    const formatNumber = (value, options = {}) => {
      const {
        locale = defaultLocale,
        minimumFractionDigits = 0,
        maximumFractionDigits = 2,
        useGrouping = true,
      } = options;
      const formatter = new Intl.NumberFormat(locale, {
        minimumFractionDigits,
        maximumFractionDigits,
        useGrouping,
      });
      return formatter.format(value);
    };
    /**
     * Formats a date with locale-specific formatting
     */
    const formatDate = (date, format = 'medium') => {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      const formatter = new Intl.DateTimeFormat(defaultLocale, {
        dateStyle: format,
      });
      return formatter.format(dateObj);
    };
    /**
     * Formats a time with locale-specific formatting
     */
    const formatTime = (date, format = 'short') => {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      const formatter = new Intl.DateTimeFormat(defaultLocale, {
        timeStyle: format,
      });
      return formatter.format(dateObj);
    };
    /**
     * Formats relative time (e.g., "2 hours ago", "in 3 days")
     */
    const formatRelativeTime = date => {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
      // Use Intl.RelativeTimeFormat if available
      if (typeof Intl.RelativeTimeFormat !== 'undefined') {
        const rtf = new Intl.RelativeTimeFormat(defaultLocale, { numeric: 'auto' });
        const intervals = [
          { unit: 'year', seconds: 31536000 },
          { unit: 'month', seconds: 2592000 },
          { unit: 'day', seconds: 86400 },
          { unit: 'hour', seconds: 3600 },
          { unit: 'minute', seconds: 60 },
          { unit: 'second', seconds: 1 },
        ];
        for (const interval of intervals) {
          const count = Math.floor(Math.abs(diffInSeconds) / interval.seconds);
          if (count >= 1) {
            return rtf.format(diffInSeconds > 0 ? -count : count, interval.unit);
          }
        }
        return rtf.format(0, 'second');
      }
      // Fallback for older browsers
      const absSeconds = Math.abs(diffInSeconds);
      const isFuture = diffInSeconds < 0;
      if (absSeconds < 60) return isFuture ? 'in a few seconds' : 'a few seconds ago';
      if (absSeconds < 3600) {
        const minutes = Math.floor(absSeconds / 60);
        return isFuture
          ? `in ${minutes} minute${minutes > 1 ? 's' : ''}`
          : `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
      }
      if (absSeconds < 86400) {
        const hours = Math.floor(absSeconds / 3600);
        return isFuture
          ? `in ${hours} hour${hours > 1 ? 's' : ''}`
          : `${hours} hour${hours > 1 ? 's' : ''} ago`;
      }
      const days = Math.floor(absSeconds / 86400);
      return isFuture
        ? `in ${days} day${days > 1 ? 's' : ''}`
        : `${days} day${days > 1 ? 's' : ''} ago`;
    };
    return {
      formatCurrency,
      formatPercent,
      formatNumber,
      formatDate,
      formatTime,
      formatRelativeTime,
    };
  }, [defaultLocale]);
  return formatters;
};
export default useDataFormatting;
//# sourceMappingURL=useDataFormatting.js.map
