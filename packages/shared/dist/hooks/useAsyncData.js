/**
 * useAsyncData Hook
 *
 * A hook for handling async data fetching with loading, error, and data states.
 */
import { useState, useCallback, useEffect } from 'react';
/**
 * useAsyncData Hook
 *
 * @param asyncFn - The async function to call
 * @param options - Options for the hook
 * @returns An object with data, loading, and error states, plus a fetch function
 */
export function useAsyncData(asyncFn, options = {}) {
  const { fetchOnMount = true, dependencies = [] } = options;
  const [state, setState] = useState({
    data: null,
    isLoading: false,
    error: null,
    isInitialized: false,
  });
  const fetchData = useCallback(
    async (...params) => {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      try {
        const data = await asyncFn(...params);
        setState({
          data,
          isLoading: false,
          error: null,
          isInitialized: true,
        });
        return data;
      } catch (error) {
        const errorObject = error instanceof Error ? error : new Error(String(error));
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorObject,
          isInitialized: true,
        }));
        throw errorObject;
      }
    },
    [asyncFn]
  );
  useEffect(() => {
    if (fetchOnMount) {
      fetchData(...[]);
    }
  }, [fetchOnMount, fetchData, ...dependencies]);
  return {
    ...state,
    fetchData,
    refetch: () => fetchData(...[]),
  };
}
//# sourceMappingURL=useAsyncData.js.map
