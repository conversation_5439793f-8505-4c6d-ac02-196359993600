/**
 * useLocalStorage Hook
 *
 * Custom hook for managing localStorage.
 */
import { useState, useEffect } from 'react';
/**
 * Custom hook for managing localStorage
 * @param key - The localStorage key
 * @param initialValue - The initial value
 * @returns [storedValue, setValue] - The stored value and a function to update it
 */
export function useLocalStorage(key, initialValue) {
  // Get from localStorage then
  // parse stored json or return initialValue
  const readValue = () => {
    // Prevent build error "window is undefined" but keep working
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  };
  // State to store our value
  // Pass initial state function to useState so logic is only executed once
  const [storedValue, setStoredValue] = useState(readValue);
  // Return a wrapped version of useState's setter function that ...
  // ... persists the new value to localStorage.
  const setValue = value => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      // Save to state
      setStoredValue(valueToStore);
      // Save to localStorage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };
  // Listen to changes in localStorage
  useEffect(() => {
    const handleStorageChange = event => {
      if (event.key === key && event.newValue) {
        setStoredValue(JSON.parse(event.newValue));
      }
    };
    // Listen for changes to this localStorage key
    window.addEventListener('storage', handleStorageChange);
    // Remove event listener on cleanup
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);
  return [storedValue, setValue];
}
//# sourceMappingURL=useLocalStorage.js.map
