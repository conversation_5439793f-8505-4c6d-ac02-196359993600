/**
 * useDataSection Hook
 *
 * EXTRACTED FROM: Multiple dashboard and data display components
 * Standardizes data section patterns with loading, error, and empty states.
 *
 * BENEFITS:
 * - Consistent data section behavior
 * - Built-in loading/error/empty state handling
 * - Automatic refresh capabilities
 * - Type-safe data operations
 * - Reduced boilerplate in data components
 */
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useLoadingState } from './useLoadingState';
/**
 * Default empty check function
 */
const defaultIsEmpty = data => {
  if (data === null || data === undefined) return true;
  if (Array.isArray(data)) return data.length === 0;
  if (typeof data === 'object') return Object.keys(data).length === 0;
  if (typeof data === 'string') return data.trim().length === 0;
  return false;
};
/**
 * Custom hook for managing data sections with loading, error, and empty states
 *
 * @param config - Configuration object for the data section
 * @returns Object with data state and actions
 *
 * @example
 * ```typescript
 * const tradesSection = useDataSection({
 *   fetchData: () => tradeApi.getTrades(),
 *   fetchOnMount: true,
 *   refreshInterval: 30000, // 30 seconds
 *   isEmpty: (trades) => trades.length === 0,
 *   dependencies: [filters], // Refetch when filters change
 * });
 *
 * if (tradesSection.isLoading) return <LoadingSpinner />;
 * if (tradesSection.error) return <ErrorMessage error={tradesSection.error} />;
 * if (tradesSection.isEmpty) return <EmptyState />;
 *
 * return <TradesList trades={tradesSection.data} />;
 * ```
 */
export const useDataSection = config => {
  const {
    fetchData,
    initialData = null,
    fetchOnMount = true,
    refreshInterval,
    isEmpty = defaultIsEmpty,
    transformError,
    dependencies = [],
  } = config;
  // State
  const [data, setData] = useState(initialData);
  const [lastFetched, setLastFetched] = useState(null);
  // Loading state management
  const loadingState = useLoadingState();
  // Computed state
  const isDataEmpty = useMemo(() => {
    return data === null || isEmpty(data);
  }, [data, isEmpty]);
  /**
   * Fetches data and updates state
   */
  const fetchDataInternal = useCallback(async () => {
    try {
      const result = await loadingState.withLoading(fetchData);
      setData(result);
      setLastFetched(new Date());
    } catch (error) {
      const errorMessage =
        transformError && error instanceof Error
          ? transformError(error)
          : error instanceof Error
          ? error.message
          : 'Failed to fetch data';
      loadingState.setError(errorMessage);
      console.error('Data fetch failed:', error);
    }
  }, [fetchData, loadingState, transformError]);
  /**
   * Public refresh function
   */
  const refresh = useCallback(async () => {
    await fetchDataInternal();
  }, [fetchDataInternal]);
  /**
   * Resets the entire state
   */
  const reset = useCallback(() => {
    setData(initialData);
    setLastFetched(null);
    loadingState.reset();
  }, [initialData, loadingState]);
  /**
   * Manually set data (useful for optimistic updates)
   */
  const setDataManually = useCallback(
    newData => {
      setData(newData);
      setLastFetched(new Date());
      loadingState.clearError();
    },
    [loadingState]
  );
  // Fetch on mount
  useEffect(() => {
    if (fetchOnMount) {
      fetchDataInternal();
    }
  }, [fetchOnMount, fetchDataInternal]);
  // Refetch when dependencies change
  useEffect(() => {
    if (dependencies.length > 0 && lastFetched !== null) {
      fetchDataInternal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);
  // Set up refresh interval
  useEffect(() => {
    if (!refreshInterval || refreshInterval <= 0) return;
    const interval = setInterval(() => {
      // Only refresh if not currently loading and no error
      if (!loadingState.isLoading && !loadingState.error) {
        fetchDataInternal();
      }
    }, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval, loadingState.isLoading, loadingState.error, fetchDataInternal]);
  return {
    // State
    data,
    isLoading: loadingState.isLoading,
    error: loadingState.error,
    isEmpty: isDataEmpty,
    isSuccess: loadingState.isSuccess,
    isError: loadingState.isError,
    lastFetched,
    // Actions
    refresh,
    clearError: loadingState.clearError,
    reset,
    setData: setDataManually,
  };
};
export default useDataSection;
//# sourceMappingURL=useDataSection.js.map
