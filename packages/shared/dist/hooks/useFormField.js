/**
 * useFormField Hook
 *
 * EXTRACTED FROM: Multiple form components with repetitive field logic
 * Standardizes form field behavior, validation, and state management.
 *
 * BENEFITS:
 * - Consistent form field behavior
 * - Built-in validation patterns
 * - Automatic error state management
 * - Type-safe field values
 * - Reduced boilerplate in form components
 */
import { useState, useCallback, useMemo } from 'react';
/**
 * Built-in validation rules for common patterns
 */
export const validationRules = {
  required: (message = 'This field is required') => ({
    validate: value => {
      if (typeof value === 'string') return value.trim().length > 0;
      if (typeof value === 'number') return !isNaN(value);
      if (Array.isArray(value)) return value.length > 0;
      return value != null && value !== undefined;
    },
    message,
  }),
  email: (message = 'Please enter a valid email address') => ({
    validate: value => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    message,
  }),
  minLength: (min, message) => ({
    validate: value => value.length >= min,
    message: message || `Must be at least ${min} characters`,
  }),
  maxLength: (max, message) => ({
    validate: value => value.length <= max,
    message: message || `Must be no more than ${max} characters`,
  }),
  min: (min, message) => ({
    validate: value => value >= min,
    message: message || `Must be at least ${min}`,
  }),
  max: (max, message) => ({
    validate: value => value <= max,
    message: message || `Must be no more than ${max}`,
  }),
  pattern: (regex, message) => ({
    validate: value => regex.test(value),
    message,
  }),
};
/**
 * Custom hook for managing form field state and validation
 *
 * @param config - Configuration object for the form field
 * @returns Object with field state and actions
 *
 * @example
 * ```typescript
 * const emailField = useFormField({
 *   initialValue: '',
 *   required: true,
 *   type: 'email',
 *   validationRules: [
 *     validationRules.required(),
 *     validationRules.email(),
 *   ],
 *   validateOnBlur: true,
 * });
 *
 * return (
 *   <input
 *     type="email"
 *     value={emailField.value}
 *     onChange={emailField.handleChange}
 *     onBlur={emailField.handleBlur}
 *   />
 * );
 * ```
 */
export const useFormField = (config = {}) => {
  const {
    initialValue = '',
    required = false,
    type = 'text',
    validationRules: rules = [],
    validateOnChange = false,
    validateOnBlur = true,
    transform,
  } = config;
  // Combine required rule with custom rules
  const allRules = useMemo(() => {
    const combinedRules = [...rules];
    if (required && !rules.some(rule => rule.message.toLowerCase().includes('required'))) {
      combinedRules.unshift(validationRules.required());
    }
    return combinedRules;
  }, [required, rules]);
  // State
  const [value, setValue] = useState(initialValue);
  const [error, setError] = useState(null);
  const [touched, setTouched] = useState(false);
  const [validating, setValidating] = useState(false);
  // Computed state
  const dirty = useMemo(() => value !== initialValue, [value, initialValue]);
  const valid = useMemo(() => error === null && !validating, [error, validating]);
  const isValid = useMemo(() => error === null && !validating, [error, validating]);
  /**
   * Validates the current value against all rules
   */
  const validate = useCallback(async () => {
    setValidating(true);
    try {
      for (const rule of allRules) {
        if (!rule.validate(value)) {
          setError(rule.message);
          setValidating(false);
          return false;
        }
      }
      setError(null);
      setValidating(false);
      return true;
    } catch (err) {
      setError('Validation error occurred');
      setValidating(false);
      return false;
    }
  }, [value, allRules]);
  /**
   * Resets the field to its initial state
   */
  const reset = useCallback(() => {
    setValue(initialValue);
    setError(null);
    setTouched(false);
    setValidating(false);
  }, [initialValue]);
  /**
   * Handles input change events
   */
  const handleChange = useCallback(
    e => {
      let newValue;
      if (type === 'number') {
        newValue = parseFloat(e.target.value) || 0;
      } else {
        newValue = e.target.value;
      }
      // Apply transform if provided
      if (transform) {
        newValue = transform(newValue);
      }
      setValue(newValue);
      // Validate on change if enabled
      if (validateOnChange && touched) {
        // Use setTimeout to avoid blocking the UI
        setTimeout(() => validate(), 0);
      }
    },
    [type, transform, validateOnChange, touched, validate]
  );
  /**
   * Handles input blur events
   */
  const handleBlur = useCallback(
    _e => {
      setTouched(true);
      // Validate on blur if enabled
      if (validateOnBlur) {
        validate();
      }
    },
    [validateOnBlur, validate]
  );
  return {
    // State
    value,
    error,
    touched,
    dirty,
    valid,
    isValid,
    validating,
    // Actions
    setValue,
    setError,
    setTouched,
    validate,
    reset,
    handleChange,
    handleBlur,
  };
};
export default useFormField;
//# sourceMappingURL=useFormField.js.map
