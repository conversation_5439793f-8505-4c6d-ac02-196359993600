/**
 * useSortableTable Hook
 *
 * EXTRACTED FROM: CategoryPerformanceChart and other table components
 * Centralizes sortable table logic for consistent behavior across components.
 *
 * BENEFITS:
 * - Consistent sorting behavior
 * - Type-safe sorting configuration
 * - Reduced boilerplate in table components
 * - Automatic sort state management
 */
import { useState, useMemo, useCallback } from 'react';
/**
 * Default sort functions for common data types
 */
export const sortFunctions = {
  string: field => (a, b) => {
    const aVal = String(a[field] || '');
    const bVal = String(b[field] || '');
    return aVal.localeCompare(bVal);
  },
  number: field => (a, b) => {
    const aVal = Number(a[field]) || 0;
    const bVal = Number(b[field]) || 0;
    return aVal - bVal;
  },
  date: field => (a, b) => {
    const aVal = new Date(a[field]).getTime();
    const bVal = new Date(b[field]).getTime();
    return aVal - bVal;
  },
  boolean: field => (a, b) => {
    const aVal = Boolean(a[field]);
    const bVal = Boolean(b[field]);
    return Number(aVal) - Number(bVal);
  },
};
/**
 * Custom hook for managing sortable table state and logic
 *
 * @param config - Configuration object for the sortable table
 * @returns Object with sorted data and sort management functions
 *
 * @example
 * ```typescript
 * const tableConfig = useSortableTable({
 *   data: trades,
 *   columns: [
 *     { field: 'symbol', label: 'Symbol', sortable: true },
 *     { field: 'profitLoss', label: 'P&L', sortable: true, sortFn: sortFunctions.number('profitLoss') },
 *     { field: 'date', label: 'Date', sortable: true, sortFn: sortFunctions.date('date') },
 *   ],
 *   defaultSort: { field: 'profitLoss', direction: 'desc' },
 * });
 *
 * // In component render:
 * <th onClick={() => tableConfig.handleSort('symbol')}>
 *   Symbol {tableConfig.getSortIcon('symbol')}
 * </th>
 * ```
 */
export const useSortableTable = ({ data, columns, defaultSort }) => {
  const [sortConfig, setSortConfig] = useState(
    defaultSort ? { field: defaultSort.field, direction: defaultSort.direction } : null
  );
  /**
   * Handles sorting when a column header is clicked
   */
  const handleSort = useCallback(
    field => {
      const column = columns.find(col => col.field === field);
      if (!column?.sortable) return;
      setSortConfig(current => {
        if (current?.field === field) {
          // Toggle direction if same field
          return {
            field,
            direction: current.direction === 'asc' ? 'desc' : 'asc',
          };
        } else {
          // Set new field with default direction (desc for numbers, asc for strings)
          const defaultDirection = typeof data[0]?.[field] === 'number' ? 'desc' : 'asc';
          return {
            field,
            direction: defaultDirection,
          };
        }
      });
    },
    [columns, data]
  );
  /**
   * Sorts the data based on current sort configuration
   */
  const sortedData = useMemo(() => {
    if (!sortConfig) return data;
    const column = columns.find(col => col.field === sortConfig.field);
    if (!column) return data;
    const sortedArray = [...data].sort((a, b) => {
      let comparison = 0;
      if (column.sortFn) {
        // Use custom sort function if provided
        comparison = column.sortFn(a, b);
      } else {
        // Use default sort based on data type
        const aVal = a[sortConfig.field];
        const bVal = b[sortConfig.field];
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          comparison = aVal.localeCompare(bVal);
        } else if (typeof aVal === 'number' && typeof bVal === 'number') {
          comparison = aVal - bVal;
        } else {
          // Fallback to string comparison
          comparison = String(aVal).localeCompare(String(bVal));
        }
      }
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
    return sortedArray;
  }, [data, sortConfig, columns]);
  /**
   * Gets the sort icon for a specific field
   */
  const getSortIcon = useCallback(
    field => {
      if (!sortConfig || sortConfig.field !== field) return null;
      return sortConfig.direction === 'asc' ? '↑' : '↓';
    },
    [sortConfig]
  );
  /**
   * Checks if a field is currently being sorted
   */
  const isSorted = useCallback(
    field => {
      return sortConfig?.field === field;
    },
    [sortConfig]
  );
  /**
   * Gets the sort direction for a specific field
   */
  const getSortDirection = useCallback(
    field => {
      return sortConfig?.field === field ? sortConfig.direction : null;
    },
    [sortConfig]
  );
  return {
    sortedData,
    sortConfig,
    handleSort,
    getSortIcon,
    isSorted,
    getSortDirection,
  };
};
export default useSortableTable;
//# sourceMappingURL=useSortableTable.js.map
