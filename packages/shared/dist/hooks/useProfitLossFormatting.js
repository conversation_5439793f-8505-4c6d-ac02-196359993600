/**
 * useProfitLossFormatting Hook
 *
 * EXTRACTED FROM: ProfitLossCell.tsx (reducing complexity)
 * Custom hook for handling profit/loss formatting logic and state determination.
 */
import { useMemo } from 'react';
/**
 * Formats a number as currency
 */
const formatCurrency = (amount, currency = '$', showPositiveSign = false) => {
  const absAmount = Math.abs(amount);
  const formattedAmount = absAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  if (amount > 0) {
    return showPositiveSign ? `+${currency}${formattedAmount}` : `${currency}${formattedAmount}`;
  } else if (amount < 0) {
    return `-${currency}${formattedAmount}`;
  } else {
    return `${currency}${formattedAmount}`;
  }
};
/**
 * Custom hook for profit/loss formatting and state determination
 */
export const useProfitLossFormatting = (amount, options = {}) => {
  const { currency = '$', showPositiveSign = false, customAriaLabel } = options;
  return useMemo(() => {
    // Handle empty/null/undefined cases
    if (amount === null || amount === undefined) {
      return {
        formattedAmount: '',
        isProfit: false,
        isLoss: false,
        isNeutral: false,
        isEmpty: true,
        ariaLabel: customAriaLabel || 'No profit/loss data available',
      };
    }
    // Determine state
    const isProfit = amount > 0;
    const isLoss = amount < 0;
    const isNeutral = amount === 0;
    // Format amount
    const formattedAmount = formatCurrency(amount, currency, showPositiveSign);
    // Generate aria label
    const defaultAriaLabel = `${
      isProfit ? 'Profit' : isLoss ? 'Loss' : 'Breakeven'
    } of ${formattedAmount}`;
    return {
      formattedAmount,
      isProfit,
      isLoss,
      isNeutral,
      isEmpty: false,
      ariaLabel: customAriaLabel || defaultAriaLabel,
    };
  }, [amount, currency, showPositiveSign, customAriaLabel]);
};
export default useProfitLossFormatting;
//# sourceMappingURL=useProfitLossFormatting.js.map
