import { jsx as _jsx } from 'react/jsx-runtime';
/**
 * Create Store Context
 *
 * A utility for creating a typed context store with actions and selectors.
 */
import { createContext, useContext, useReducer, useMemo } from 'react';
/**
 * Create a store context with a reducer
 *
 * @param reducer - The reducer function
 * @param initialState - The initial state
 * @param displayName - The display name for the context
 * @returns An object with the context, provider, and hooks
 */
export function createStoreContext(reducer, initialState, displayName = 'StoreContext') {
  // Create the context
  const Context = createContext(undefined);
  Context.displayName = displayName;
  // Create the provider
  const Provider = ({ children, initialState: overrideInitialState }) => {
    const [state, dispatch] = useReducer(reducer, overrideInitialState || initialState);
    const value = useMemo(() => ({ state, dispatch }), [state]);
    return _jsx(Context.Provider, { value: value, children: children });
  };
  // Create the hook to use the context
  function useStore() {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error(`use${displayName} must be used within a ${displayName}Provider`);
    }
    return context;
  }
  // Create a hook to use a selector
  function useSelector(selector) {
    const { state } = useStore();
    return selector(state);
  }
  // Create a hook to use an action creator
  function useAction(actionCreator) {
    const { dispatch } = useStore();
    return useMemo(
      () =>
        (...args) => {
          dispatch(actionCreator(...args));
        },
      [dispatch, actionCreator]
    );
  }
  // Create a hook to use multiple action creators
  function useActions(actionCreators) {
    const { dispatch } = useStore();
    return useMemo(() => {
      const boundActionCreators = {};
      for (const key in actionCreators) {
        boundActionCreators[key] = (...args) => {
          dispatch(actionCreators[key](...args));
        };
      }
      return boundActionCreators;
    }, [dispatch, actionCreators]);
  }
  return {
    Context,
    Provider,
    useStore,
    useSelector,
    useAction,
    useActions,
  };
}
//# sourceMappingURL=createStoreContext.js.map
