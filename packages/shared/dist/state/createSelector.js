/**
 * Create Selector
 *
 * A utility for creating memoized selectors.
 */
export function createSelector(...args) {
  const resultFunc = args.pop();
  const inputSelectors = args;
  let lastInputs = null;
  let lastResult = null;
  return state => {
    const inputs = inputSelectors.map(selector => selector(state));
    // Check if inputs have changed
    if (
      lastInputs === null ||
      inputs.length !== lastInputs.length ||
      inputs.some((input, index) => input !== lastInputs[index])
    ) {
      // Apply the result function to the new inputs
      lastResult = resultFunc(...inputs);
      lastInputs = inputs;
    }
    return lastResult;
  };
}
//# sourceMappingURL=createSelector.js.map
