/**
 * Theme System
 *
 * This file exports the theme system for the ADHD Trading Dashboard.
 * Centralized theme architecture with CSS variable generation.
 */
// Export theme types
export * from './types';
export * from './profitLossTheme';
// Export theme tokens
export * from './tokens';
// Export theme variants
export { f1Theme as mercedesGreenTheme } from './f1Theme';
export { f1OfficialTheme } from './f1OfficialTheme';
export { lightTheme } from './lightTheme';
export { darkTheme } from './darkTheme';
// Legacy export for backward compatibility
export { f1Theme } from './f1Theme';
// Export theme provider and context
export * from './ThemeProvider';
// Export CSS generation utilities
export * from './css-generator';
// Export unified theme configuration
export const AVAILABLE_THEMES = {
  'mercedes-green': () => import('./f1Theme').then(m => m.f1Theme),
  'f1-official': () => import('./f1OfficialTheme').then(m => m.f1OfficialTheme),
  dark: () => import('./darkTheme').then(m => m.darkTheme),
};
//# sourceMappingURL=index.js.map
