/**
 * Color Tokens
 *
 * This file defines the base color tokens used throughout the application.
 */
/**
 * Base Colors
 *
 * These are the raw color values used as the foundation for the theme.
 */
export const baseColors = {
  // F1 Racing Team Colors
  f1Red: '#e10600', // Ferrari Red - CRITICAL ALERTS ONLY
  f1RedDark: '#c10500',
  f1RedLight: '#ff3b36',
  f1Blue: '#0600EF', // Racing Blue - Information & Neutral
  f1BlueDark: '#0500CC',
  f1BlueLight: '#4169E1',
  // F1 Racing Performance Colors
  f1MercedesGreen: '#00D2BE', // Active, Success, Optimal
  f1MercedesGreenDark: '#00A896',
  f1MercedesGreenLight: '#00FFE5',
  f1McLarenOrange: '#FF8700', // Warnings, Transitions
  f1McLarenOrangeDark: '#E67600',
  f1McLarenOrangeLight: '#FFA500',
  f1RacingYellow: '#FFD320', // Caution, Pending
  f1RacingYellowDark: '#E6BE1D',
  f1RacingYellowLight: '#FFDC4A',
  f1Carbon: '#1A1A1A', // Base background
  f1Silver: '#C0C0C0', // Secondary text
  // Neutrals
  white: '#ffffff',
  black: '#000000',
  gray100: '#f5f5f5',
  gray200: '#e0e0e0',
  gray300: '#cccccc',
  gray400: '#aaaaaa',
  gray500: '#757575',
  gray600: '#666666',
  gray700: '#333333',
  gray800: '#1a1f2c',
  gray900: '#121212',
  // Status colors
  green: '#4caf50',
  greenDark: '#388e3c',
  greenLight: '#81c784',
  yellow: '#ffeb3b',
  yellowDark: '#fbc02d',
  yellowLight: '#fff176',
  orange: '#ff9800',
  orangeDark: '#f57c00',
  orangeLight: '#ffb74d',
  red: '#f44336',
  redDark: '#d32f2f',
  redLight: '#e57373',
  purple: '#9c27b0',
  purpleDark: '#7b1fa2',
  purpleLight: '#ba68c8',
  // Transparent colors
  transparent: 'transparent',
  blackTransparent10: 'rgba(0, 0, 0, 0.1)',
  blackTransparent20: 'rgba(0, 0, 0, 0.2)',
  blackTransparent50: 'rgba(0, 0, 0, 0.5)',
  whiteTransparent10: 'rgba(255, 255, 255, 0.1)',
  whiteTransparent20: 'rgba(255, 255, 255, 0.2)',
  whiteTransparent50: 'rgba(255, 255, 255, 0.5)',
};
/**
 * Semantic Colors - Dark Mode
 *
 * These map the base colors to semantic meanings for dark mode.
 */
export const darkModeColors = {
  // Background colors
  background: baseColors.gray800,
  surface: baseColors.gray700,
  surfaceHover: baseColors.gray600,
  surfaceActive: baseColors.gray500,
  // Text colors - Improved contrast for dark backgrounds
  textPrimary: baseColors.white, // #ffffff - High contrast
  textSecondary: baseColors.gray300, // #d1d5db - Better visibility
  textDisabled: baseColors.gray500, // #6b7280 - Still visible but muted
  textInverse: baseColors.gray800,
  // Border colors
  border: baseColors.gray700,
  borderHover: baseColors.gray600,
  borderFocus: baseColors.f1Red,
  // Status colors
  success: baseColors.green,
  warning: baseColors.yellow,
  error: baseColors.red,
  info: baseColors.f1Blue,
  // Trading specific colors
  profit: baseColors.green,
  loss: baseColors.red,
  neutral: baseColors.gray400,
  // Component specific colors
  tooltipBackground: 'rgba(37, 42, 55, 0.9)',
  modalBackground: 'rgba(26, 31, 44, 0.8)',
  chartGrid: baseColors.whiteTransparent10,
  chartLine: baseColors.f1Red,
};
/**
 * Semantic Colors - Light Mode
 *
 * These map the base colors to semantic meanings for light mode.
 */
export const lightModeColors = {
  // Background colors
  background: baseColors.gray100,
  surface: baseColors.white,
  surfaceHover: baseColors.gray100,
  surfaceActive: baseColors.gray200,
  // Text colors
  textPrimary: baseColors.gray900,
  textSecondary: baseColors.gray600,
  textDisabled: baseColors.gray400,
  textInverse: baseColors.white,
  // Border colors
  border: baseColors.gray200,
  borderHover: baseColors.gray300,
  borderFocus: baseColors.f1Red,
  // Status colors
  success: baseColors.greenDark,
  warning: baseColors.yellowDark,
  error: baseColors.redDark,
  info: baseColors.f1BlueDark,
  // Trading specific colors
  profit: baseColors.greenDark,
  loss: baseColors.redDark,
  neutral: baseColors.gray500,
  // Component specific colors
  tooltipBackground: 'rgba(0, 0, 0, 0.8)',
  modalBackground: 'rgba(0, 0, 0, 0.5)',
  chartGrid: baseColors.blackTransparent10,
  chartLine: baseColors.f1Red,
};
//# sourceMappingURL=colors.js.map
