/**
 * Theme Tokens
 *
 * This module exports all the design tokens used in the theme.
 */
export * from './colors';
export * from './spacing';
export * from './typography';
/**
 * Breakpoints
 *
 * These are the breakpoint values used for responsive design.
 */
export const breakpoints = {
  xs: '480px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
};
/**
 * Border Radius
 *
 * These are the border radius values used throughout the application.
 */
export const borderRadius = {
  xs: '2px',
  sm: '4px',
  md: '6px',
  lg: '8px',
  xl: '12px',
  pill: '9999px',
  circle: '50%',
};
/**
 * Shadows
 *
 * These are the shadow values used throughout the application.
 */
export const shadows = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
  md: '0 4px 6px rgba(0, 0, 0, 0.1)',
  lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
};
/**
 * Transitions
 *
 * These are the transition values used throughout the application.
 */
export const transitions = {
  fast: '0.1s',
  normal: '0.3s',
  slow: '0.5s',
};
/**
 * Z-Index
 *
 * These are the z-index values used throughout the application.
 */
export const zIndex = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100,
};
//# sourceMappingURL=index.js.map
