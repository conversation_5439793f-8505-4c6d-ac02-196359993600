/**
 * CSS Variables Generator
 *
 * Generates CSS variables from theme objects to create a single source of truth
 * for styling across the entire application.
 */
/**
 * Converts a theme object to CSS variables
 */
export function generateCSSVariables(theme) {
  const cssVars = [];
  // Add theme name as data attribute selector
  cssVars.push(`[data-theme="${theme.name}"] {`);
  // Colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    cssVars.push(`  --color-${kebabCase(key)}: ${value};`);
  });
  // Spacing
  Object.entries(theme.spacing).forEach(([key, value]) => {
    cssVars.push(`  --spacing-${key}: ${value};`);
  });
  // Font sizes
  Object.entries(theme.fontSizes).forEach(([key, value]) => {
    cssVars.push(`  --font-size-${key}: ${value};`);
  });
  // Font weights
  Object.entries(theme.fontWeights).forEach(([key, value]) => {
    cssVars.push(`  --font-weight-${key}: ${value};`);
  });
  // Font families
  Object.entries(theme.fontFamilies).forEach(([key, value]) => {
    cssVars.push(`  --font-family-${key}: ${value};`);
  });
  // Border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    cssVars.push(`  --border-radius-${key}: ${value};`);
  });
  // Shadows
  Object.entries(theme.shadows).forEach(([key, value]) => {
    cssVars.push(`  --shadow-${key}: ${value};`);
  });
  // Transitions
  Object.entries(theme.transitions).forEach(([key, value]) => {
    cssVars.push(`  --transition-${key}: ${value};`);
  });
  // Z-index
  Object.entries(theme.zIndex).forEach(([key, value]) => {
    cssVars.push(`  --z-index-${key}: ${value};`);
  });
  cssVars.push('}');
  return cssVars.join('\n');
}
/**
 * Generates semantic CSS variables for components
 */
export function generateSemanticVariables(theme) {
  const cssVars = [];
  cssVars.push(`[data-theme="${theme.name}"] {`);
  // Component semantic mappings
  cssVars.push(`  /* Component Semantic Variables */`);
  cssVars.push(`  --primary-color: var(--color-primary);`);
  cssVars.push(`  --secondary-color: var(--color-secondary);`);
  cssVars.push(`  --accent-color: var(--color-accent);`);
  cssVars.push(`  --success-color: var(--color-success);`);
  cssVars.push(`  --warning-color: var(--color-warning);`);
  cssVars.push(`  --error-color: var(--color-error);`);
  cssVars.push(`  --info-color: var(--color-info);`);
  // Background semantic mappings
  cssVars.push(`  --bg-primary: var(--color-background);`);
  cssVars.push(`  --bg-secondary: var(--color-surface);`);
  cssVars.push(`  --bg-card: var(--color-card-background);`);
  cssVars.push(`  --bg-elevated: var(--color-elevated);`);
  // Text semantic mappings
  cssVars.push(`  --text-primary: var(--color-text-primary);`);
  cssVars.push(`  --text-secondary: var(--color-text-secondary);`);
  cssVars.push(`  --text-disabled: var(--color-text-disabled);`);
  cssVars.push(`  --text-inverse: var(--color-text-inverse);`);
  // Border semantic mappings
  cssVars.push(`  --border-primary: var(--color-border);`);
  cssVars.push(`  --border-secondary: var(--color-divider);`);
  // Session card semantic mappings
  cssVars.push(`  --session-card-bg: var(--bg-card);`);
  cssVars.push(`  --session-card-border: var(--border-primary);`);
  cssVars.push(`  --session-card-accent: var(--primary-color);`);
  // Session state mappings
  cssVars.push(`  --session-active: var(--color-session-active);`);
  cssVars.push(`  --session-optimal: var(--color-session-optimal);`);
  cssVars.push(`  --session-caution: var(--color-session-caution);`);
  cssVars.push(`  --session-transition: var(--color-session-transition);`);
  cssVars.push(`  --session-inactive: var(--color-session-inactive);`);
  cssVars.push('}');
  return cssVars.join('\n');
}
/**
 * Converts camelCase to kebab-case
 */
function kebabCase(str) {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
}
/**
 * Generates complete CSS for all themes
 */
export function generateAllThemeCSS(themes) {
  const cssBlocks = [];
  // Add CSS header
  cssBlocks.push(`/**
 * Generated Theme CSS Variables
 * 
 * This file is auto-generated from theme definitions.
 * Do not edit manually - changes will be overwritten.
 */`);
  // Generate CSS for each theme
  Object.values(themes).forEach(theme => {
    cssBlocks.push('');
    cssBlocks.push(`/* ${theme.name} Theme */`);
    cssBlocks.push(generateCSSVariables(theme));
    cssBlocks.push('');
    cssBlocks.push(generateSemanticVariables(theme));
  });
  return cssBlocks.join('\n');
}
//# sourceMappingURL=css-generator.js.map
