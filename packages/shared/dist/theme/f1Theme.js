/**
 * Formula 1 Theme
 *
 * This file contains the Formula 1 inspired theme for the ADHD Trading Dashboard.
 */
import {
  baseColors,
  darkModeColors,
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from './tokens';
/**
 * Mercedes F1 Theme
 *
 * Pure Mercedes F1 team theme with Petronas teal/green colors.
 * Authentic Mercedes-AMG Petronas Formula One Team aesthetic.
 */
export const f1Theme = {
  name: 'mercedes-green',
  colors: {
    // Primary colors - Mercedes Green for active/positive states
    primary: baseColors.f1MercedesGreen,
    primaryDark: baseColors.f1MercedesGreenDark,
    primaryLight: baseColors.f1MercedesGreenLight,
    // Secondary colors - Racing Blue for information
    secondary: baseColors.f1Blue,
    secondaryDark: baseColors.f1BlueDark,
    secondaryLight: baseColors.f1BlueLight,
    // Accent colors - McLaren Orange for transitions
    accent: baseColors.f1McLarenOrange,
    accentDark: baseColors.f1McLarenOrangeDark,
    accentLight: baseColors.f1McLarenOrangeLight,
    // F1 Racing Status Colors
    success: baseColors.f1MercedesGreen, // Green flag - optimal performance
    warning: baseColors.f1McLarenOrange, // Orange flag - caution/transitions
    error: baseColors.f1Red, // Red flag - critical alerts only
    danger: baseColors.f1Red, // Ferrari red for genuine danger
    info: baseColors.f1Blue, // Racing blue for information
    // Neutral colors
    background: darkModeColors.background,
    surface: darkModeColors.surface,
    elevated: baseColors.gray700, // Added elevated color for F1 theme
    cardBackground: darkModeColors.surface,
    border: darkModeColors.border,
    divider: 'rgba(255, 255, 255, 0.1)',
    // Text colors
    textPrimary: darkModeColors.textPrimary,
    textSecondary: darkModeColors.textSecondary,
    textDisabled: darkModeColors.textDisabled,
    textInverse: darkModeColors.textInverse,
    // Chart colors
    chartGrid: darkModeColors.chartGrid,
    chartLine: darkModeColors.chartLine,
    chartAxis: baseColors.gray400,
    chartTooltip: darkModeColors.tooltipBackground,
    // F1 Racing Trading Colors
    profit: baseColors.f1MercedesGreen, // Green flag for profitable trades
    loss: baseColors.f1Red, // Red flag for losses only
    neutral: baseColors.f1Silver, // Silver for neutral data
    // F1 Racing Tab Colors
    tabActive: baseColors.f1MercedesGreen, // Active tabs use Mercedes green
    tabInactive: baseColors.gray600,
    // Component specific colors
    tooltipBackground: darkModeColors.tooltipBackground,
    modalBackground: darkModeColors.modalBackground,
    sidebarBackground: baseColors.gray800,
    headerBackground: 'rgba(0, 0, 0, 0.2)',
    // F1 Racing Session States
    sessionActive: baseColors.f1MercedesGreen, // Active sessions - green flag
    sessionOptimal: baseColors.f1MercedesGreenLight, // Optimal windows - bright green
    sessionCaution: baseColors.f1RacingYellow, // Caution periods - yellow flag
    sessionTransition: baseColors.f1McLarenOrange, // Transition periods - orange
    sessionInactive: baseColors.gray600, // Inactive sessions - neutral
    // F1 Racing Performance States
    performanceExcellent: baseColors.f1MercedesGreen,
    performanceGood: baseColors.f1Blue,
    performanceAverage: baseColors.f1Silver,
    performancePoor: baseColors.f1McLarenOrange,
    performanceAvoid: baseColors.f1Red,
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};
//# sourceMappingURL=f1Theme.js.map
