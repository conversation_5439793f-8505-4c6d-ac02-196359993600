import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
const MetricsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const MetricCard = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
`;
const MetricLabel = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const MetricValue = styled.div`
  color: ${({ theme, positive, negative }) =>
    positive ? theme.colors.success : negative ? theme.colors.danger : theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
`;
const TradeMetrics = ({ metrics, isLoading }) => {
  if (isLoading) {
    return _jsx(MetricsContainer, {
      children: Array.from({ length: 4 }).map((_, index) =>
        _jsxs(
          MetricCard,
          {
            children: [
              _jsx(MetricLabel, { children: 'Loading...' }),
              _jsx(MetricValue, { children: '--' }),
            ],
          },
          index
        )
      ),
    });
  }
  return _jsx(MetricsContainer, {
    children: metrics.map((metric, index) =>
      _jsxs(
        MetricCard,
        {
          children: [
            _jsx(MetricLabel, { children: metric.label }),
            _jsx(MetricValue, {
              positive: metric.positive,
              negative: metric.negative,
              children: metric.value,
            }),
          ],
        },
        index
      )
    ),
  });
};
export default TradeMetrics;
//# sourceMappingURL=TradeMetrics.js.map
