import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
const AnalysisContainer = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
`;
const AnalysisTitle = styled.h3`
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const AnalysisContent = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  line-height: 1.6;
`;
const TradeAnalysis = ({ title = 'Trade Analysis', children, isLoading }) => {
  return _jsxs(AnalysisContainer, {
    children: [
      _jsx(AnalysisTitle, { children: title }),
      _jsx(AnalysisContent, {
        children: isLoading
          ? _jsx('div', { children: 'Loading analysis...' })
          : children || _jsx('div', { children: 'No analysis data available' }),
      }),
    ],
  });
};
export default TradeAnalysis;
//# sourceMappingURL=TradeAnalysis.js.map
