import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { useState } from 'react';
import { Table } from './Table';
import { ThemeProvider } from '../../theme/ThemeProvider';
import { Button } from '../atoms/Button';
const meta = {
  title: 'Molecules/Table',
  component: Table,
  tags: ['autodocs'],
  decorators: [
    Story =>
      _jsx(ThemeProvider, {
        children: _jsx('div', {
          style: { padding: '1rem', maxWidth: '800px' },
          children: _jsx(Story, {}),
        }),
      }),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    bordered: {
      control: 'boolean',
    },
    striped: {
      control: 'boolean',
    },
    hoverable: {
      control: 'boolean',
    },
    compact: {
      control: 'boolean',
    },
    stickyHeader: {
      control: 'boolean',
    },
    isLoading: {
      control: 'boolean',
    },
    pagination: {
      control: 'boolean',
    },
    scrollable: {
      control: 'boolean',
    },
    onRowClick: { action: 'row clicked' },
    onSort: { action: 'sort changed' },
    onPageChange: { action: 'page changed' },
    onPageSizeChange: { action: 'page size changed' },
  },
};
export default meta;
const users = Array.from({ length: 50 }).map((_, index) => ({
  id: index + 1,
  name: `User ${index + 1}`,
  email: `user${index + 1}@example.com`,
  role: ['Admin', 'User', 'Editor', 'Viewer'][Math.floor(Math.random() * 4)],
  status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)],
  lastLogin: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
    .toISOString()
    .split('T')[0],
}));
// Status badge component
const StatusBadge = ({ status }) => {
  const getColor = () => {
    switch (status) {
      case 'active':
        return { bg: '#e6f7e6', text: '#2e7d32' };
      case 'inactive':
        return { bg: '#ffeaea', text: '#d32f2f' };
      case 'pending':
        return { bg: '#fff8e1', text: '#f57c00' };
      default:
        return { bg: '#f5f5f5', text: '#757575' };
    }
  };
  const { bg, text } = getColor();
  return _jsx('span', {
    style: {
      backgroundColor: bg,
      color: text,
      padding: '4px 8px',
      borderRadius: '4px',
      fontSize: '12px',
      fontWeight: 500,
      textTransform: 'capitalize',
    },
    children: status,
  });
};
// Table with pagination and sorting
const SortableTable = args => {
  const [sortColumn, setSortColumn] = useState('id');
  const [sortDirection, setSortDirection] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRow, setSelectedRow] = useState(null);
  const handleSort = (columnId, direction) => {
    setSortColumn(columnId);
    setSortDirection(direction);
    args.onSort?.(columnId, direction);
  };
  const sortedData = [...users].sort((a, b) => {
    const aValue = a[sortColumn];
    const bValue = b[sortColumn];
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
  return _jsx(Table, {
    ...args,
    columns: [
      {
        id: 'id',
        header: 'ID',
        cell: row => row.id,
        sortable: true,
        width: '60px',
      },
      {
        id: 'name',
        header: 'Name',
        cell: row => row.name,
        sortable: true,
      },
      {
        id: 'email',
        header: 'Email',
        cell: row => row.email,
        sortable: true,
      },
      {
        id: 'role',
        header: 'Role',
        cell: row => row.role,
        sortable: true,
        width: '100px',
      },
      {
        id: 'status',
        header: 'Status',
        cell: row => _jsx(StatusBadge, { status: row.status }),
        sortable: true,
        width: '100px',
        align: 'center',
      },
      {
        id: 'lastLogin',
        header: 'Last Login',
        cell: row => row.lastLogin,
        sortable: true,
        width: '120px',
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: _row =>
          _jsxs('div', {
            style: { display: 'flex', gap: '8px' },
            children: [
              _jsx(Button, { size: 'small', variant: 'outline', children: 'Edit' }),
              _jsx(Button, { size: 'small', variant: 'outline', children: 'Delete' }),
            ],
          }),
        width: '150px',
        align: 'right',
      },
    ],
    data: sortedData,
    sortColumn: sortColumn,
    sortDirection: sortDirection,
    onSort: handleSort,
    pagination: args.pagination,
    currentPage: currentPage,
    pageSize: pageSize,
    totalRows: users.length,
    onPageChange: setCurrentPage,
    onPageSizeChange: setPageSize,
    onRowClick: (row, index) => {
      setSelectedRow(selectedRow === index ? null : index);
      args.onRowClick?.(row, index);
    },
    isRowSelected: (_, index) => index === selectedRow,
  });
};
export const Default = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};
export const Loading = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: true,
    pagination: true,
    scrollable: true,
  },
};
export const Compact = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: true,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};
export const WithoutBorders = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: false,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};
export const WithoutStripes = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: true,
    striped: false,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};
export const WithStickyHeader = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: true,
    isLoading: false,
    pagination: true,
    scrollable: true,
    height: '300px',
  },
};
export const WithoutPagination = {
  render: args => _jsx(SortableTable, { ...args }),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: false,
    scrollable: true,
  },
};
export const EmptyTable = {
  render: args =>
    _jsx(Table, {
      ...args,
      columns: [
        { id: 'id', header: 'ID', cell: row => row.id },
        { id: 'name', header: 'Name', cell: row => row.name },
        { id: 'email', header: 'Email', cell: row => row.email },
      ],
      data: [],
      emptyMessage: 'No users found',
    }),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: false,
    scrollable: true,
  },
};
//# sourceMappingURL=Table.stories.js.map
