import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Trade Table Example
 *
 * Example usage of the new trade table components.
 */
import { useState, useMemo } from 'react';
import { TradeTable } from './TradeTable';
// Mock data for demonstration
const mockTradeData = [
  {
    trade: {
      id: 1,
      date: '2024-01-15',
      model_type: 'FVG-RD',
      session: 'NY Open',
      direction: 'Long',
      market: 'MNQ',
      entry_price: 16850.25,
      exit_price: 16875.5,
      r_multiple: 2.5,
      achieved_pl: 125.25,
      win_loss: 'Win',
      pattern_quality_rating: 4.2,
      entry_time: '09:35:00',
      exit_time: '10:15:00',
      notes: 'Clean FVG redelivery with strong momentum',
    },
    fvg_details: {
      trade_id: 1,
      rd_type: 'True-RD',
      entry_version: 'Simple-Entry',
      draw_on_liquidity: 'MNOR-FVG',
    },
    setup: {
      trade_id: 1,
      primary_setup: 'FVG Redelivery Setup',
      secondary_setup: 'High/Low Reversal Setup',
      liquidity_taken: 'Premarket-H/L',
    },
    analysis: {
      trade_id: 1,
      dol_target_type: 'FVG Target',
      path_quality: 'Clean',
      clustering: 'Low',
    },
  },
  {
    trade: {
      id: 2,
      date: '2024-01-15',
      model_type: 'RD-Cont',
      session: 'Lunch Macro',
      direction: 'Short',
      market: 'MNQ',
      entry_price: 16920.75,
      exit_price: 16905.25,
      r_multiple: -1.2,
      achieved_pl: -77.5,
      win_loss: 'Loss',
      pattern_quality_rating: 2.8,
      entry_time: '12:45:00',
      exit_time: '13:20:00',
      notes: 'Failed continuation, stopped out at resistance',
    },
    setup: {
      trade_id: 2,
      primary_setup: 'True-RD Continuation',
      liquidity_taken: 'Lunch-H/L',
    },
    analysis: {
      trade_id: 2,
      dol_target_type: 'Liquidity Target',
      path_quality: 'Choppy',
      clustering: 'High',
    },
  },
  {
    trade: {
      id: 3,
      date: '2024-01-16',
      model_type: 'FVG-RD',
      session: 'MOC',
      direction: 'Long',
      market: 'MNQ',
      entry_price: 16780.0,
      exit_price: 16825.75,
      r_multiple: 3.8,
      achieved_pl: 228.75,
      win_loss: 'Win',
      pattern_quality_rating: 4.8,
      entry_time: '15:50:00',
      exit_time: '16:00:00',
      notes: 'Perfect MOC setup with strong institutional flow',
    },
    fvg_details: {
      trade_id: 3,
      rd_type: 'IMM-RD',
      entry_version: 'Complex-Entry',
      draw_on_liquidity: 'AM-FPFVG',
    },
    setup: {
      trade_id: 3,
      primary_setup: 'Strong-FVG Reversal Setup',
      secondary_setup: 'Multi-Array Confluence Setup',
      liquidity_taken: 'Monthly-H/L',
    },
    analysis: {
      trade_id: 3,
      dol_target_type: 'FVG Target',
      path_quality: 'Excellent',
      clustering: 'Low',
    },
  },
];
/**
 * Basic Trade Table Example
 */
export const BasicTradeTableExample = () => {
  const [sortColumn, setSortColumn] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedTrade, setSelectedTrade] = useState(null);
  const handleSort = (columnId, direction) => {
    setSortColumn(columnId);
    setSortDirection(direction);
  };
  const handleRowClick = (trade, _index) => {
    setSelectedTrade(trade);
    console.log('Selected trade:', trade);
  };
  const isRowSelected = (trade, _index) => {
    return selectedTrade?.trade.id === trade.trade.id;
  };
  return _jsxs('div', {
    style: { padding: '20px' },
    children: [
      _jsx('h2', { children: 'Basic Trade Table' }),
      _jsx(TradeTable, {
        data: mockTradeData,
        onSort: handleSort,
        sortColumn: sortColumn,
        sortDirection: sortDirection,
        onRowClick: handleRowClick,
        isRowSelected: isRowSelected,
        expandableRows: true,
      }),
    ],
  });
};
/**
 * Filtered Trade Table Example
 */
export const FilteredTradeTableExample = () => {
  const [filters, setFilters] = useState({});
  const [sortColumn, setSortColumn] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  // Filter data based on current filters
  const filteredData = useMemo(() => {
    let filtered = [...mockTradeData];
    if (filters.dateFrom) {
      filtered = filtered.filter(trade => trade.trade.date >= filters.dateFrom);
    }
    if (filters.dateTo) {
      filtered = filtered.filter(trade => trade.trade.date <= filters.dateTo);
    }
    if (filters.model_type) {
      filtered = filtered.filter(trade => trade.trade.model_type === filters.model_type);
    }
    if (filters.session) {
      filtered = filtered.filter(trade => trade.trade.session === filters.session);
    }
    if (filters.direction) {
      filtered = filtered.filter(trade => trade.trade.direction === filters.direction);
    }
    if (filters.win_loss) {
      filtered = filtered.filter(trade => trade.trade.win_loss === filters.win_loss);
    }
    if (filters.min_r_multiple !== undefined) {
      filtered = filtered.filter(
        trade =>
          trade.trade.r_multiple !== undefined && trade.trade.r_multiple >= filters.min_r_multiple
      );
    }
    if (filters.max_r_multiple !== undefined) {
      filtered = filtered.filter(
        trade =>
          trade.trade.r_multiple !== undefined && trade.trade.r_multiple <= filters.max_r_multiple
      );
    }
    return filtered;
  }, [filters]);
  // Sort data
  const sortedData = useMemo(() => {
    const sorted = [...filteredData];
    sorted.sort((a, b) => {
      const aValue = a.trade[sortColumn];
      const bValue = b.trade[sortColumn];
      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      return 0;
    });
    return sorted;
  }, [filteredData, sortColumn, sortDirection]);
  const handleSort = (columnId, direction) => {
    setSortColumn(columnId);
    setSortDirection(direction);
  };
  const handleFiltersChange = newFilters => {
    setFilters(newFilters);
  };
  return _jsxs('div', {
    style: { padding: '20px' },
    children: [
      _jsx('h2', { children: 'Filtered Trade Table' }),
      _jsx(TradeTable, {
        data: sortedData,
        showFilters: true,
        filters: filters,
        onFiltersChange: handleFiltersChange,
        onSort: handleSort,
        sortColumn: sortColumn,
        sortDirection: sortDirection,
        expandableRows: true,
        pagination: true,
        pageSize: 10,
        totalRows: sortedData.length,
      }),
    ],
  });
};
/**
 * Compact Trade Table Example
 */
export const CompactTradeTableExample = () => {
  return _jsxs('div', {
    style: { padding: '20px' },
    children: [
      _jsx('h2', { children: 'Compact Trade Table' }),
      _jsx(TradeTable, {
        data: mockTradeData,
        columnPreset: 'compact',
        compact: true,
        height: '300px',
      }),
    ],
  });
};
/**
 * Performance Trade Table Example
 */
export const PerformanceTradeTableExample = () => {
  return _jsxs('div', {
    style: { padding: '20px' },
    children: [
      _jsx('h2', { children: 'Performance-Focused Trade Table' }),
      _jsx(TradeTable, {
        data: mockTradeData,
        columnPreset: 'performance',
        stickyHeader: true,
        height: '400px',
      }),
    ],
  });
};
/**
 * All Examples Combined
 */
export const TradeTableExamples = () => {
  const [activeExample, setActiveExample] = useState('basic');
  const examples = {
    basic: _jsx(BasicTradeTableExample, {}),
    filtered: _jsx(FilteredTradeTableExample, {}),
    compact: _jsx(CompactTradeTableExample, {}),
    performance: _jsx(PerformanceTradeTableExample, {}),
  };
  return _jsxs('div', {
    children: [
      _jsxs('div', {
        style: { padding: '20px', borderBottom: '1px solid #e5e7eb' },
        children: [
          _jsx('h1', { children: 'Trade Table Examples' }),
          _jsx('div', {
            style: { display: 'flex', gap: '10px', marginTop: '10px' },
            children: Object.keys(examples).map(key =>
              _jsx(
                'button',
                {
                  onClick: () => setActiveExample(key),
                  style: {
                    padding: '8px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '4px',
                    backgroundColor: activeExample === key ? '#3b82f6' : 'white',
                    color: activeExample === key ? 'white' : 'black',
                    cursor: 'pointer',
                  },
                  children: key.charAt(0).toUpperCase() + key.slice(1),
                },
                key
              )
            ),
          }),
        ],
      }),
      examples[activeExample],
    ],
  });
};
//# sourceMappingURL=TradeTable.example.js.map
