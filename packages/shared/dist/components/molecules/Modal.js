import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Modal Component
 *
 * A customizable modal component that follows the design system.
 */
import { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import styled, { css, keyframes } from 'styled-components';
import { Button } from '../atoms/Button';
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;
const slideIn = keyframes`
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;
const Backdrop = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ zIndex }) => zIndex || 1000};
  animation: ${fadeIn} 0.2s ease-out;
`;
const ModalContainer = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  display: flex;
  flex-direction: column;
  max-height: ${({ size }) => (size === 'fullscreen' ? '100vh' : '90vh')};
  width: ${({ size }) => {
    switch (size) {
      case 'small':
        return '400px';
      case 'medium':
        return '600px';
      case 'large':
        return '800px';
      case 'fullscreen':
        return '100vw';
      default:
        return '600px';
    }
  }};
  max-width: 95vw;
  animation: ${slideIn} 0.2s ease-out;
  position: relative;

  ${({ size }) =>
    size === 'fullscreen' &&
    css`
      height: 100vh;
      border-radius: 0;
    `}

  ${({ centered }) =>
    centered &&
    css`
      margin: auto;
    `}
`;
const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => `${theme.spacing.md} ${theme.spacing.lg}`};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const ModalTitle = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes.xl};
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary}33;
  }
`;
const ModalContent = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  ${({ scrollable }) =>
    scrollable &&
    css`
      overflow-y: auto;
      flex: 1;
    `}
`;
const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => `${theme.spacing.md} ${theme.spacing.lg}`};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;
/**
 * Modal Component
 *
 * A customizable modal component that follows the design system.
 */
export const Modal = ({
  isOpen,
  title = '',
  children,
  onClose,
  size = 'medium',
  closeOnOutsideClick = true,
  showCloseButton = true,
  footer,
  hasFooter = true,
  primaryActionText = '',
  onPrimaryAction,
  primaryActionDisabled = false,
  primaryActionLoading = false,
  secondaryActionText = '',
  onSecondaryAction,
  secondaryActionDisabled = false,
  className = '',
  zIndex = 1000,
  centered = true,
  // hasBackdrop = true, // Unused prop
  scrollable = true,
}) => {
  const modalRef = useRef(null);
  // Handle escape key press
  useEffect(() => {
    const handleKeyDown = event => {
      if (event.key === 'Escape' && isOpen && closeOnOutsideClick) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose, closeOnOutsideClick]);
  // Handle outside click
  const handleBackdropClick = event => {
    if (modalRef.current && !modalRef.current.contains(event.target) && closeOnOutsideClick) {
      onClose();
    }
  };
  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);
  // Default footer with action buttons
  const defaultFooter = _jsxs(_Fragment, {
    children: [
      secondaryActionText &&
        _jsx(Button, {
          variant: 'outline',
          onClick: onSecondaryAction,
          disabled: secondaryActionDisabled,
          children: secondaryActionText,
        }),
      primaryActionText &&
        _jsx(Button, {
          onClick: onPrimaryAction,
          disabled: primaryActionDisabled,
          loading: primaryActionLoading,
          children: primaryActionText,
        }),
    ],
  });
  if (!isOpen) return null;
  const modalContent = _jsx(Backdrop, {
    onClick: handleBackdropClick,
    zIndex: zIndex,
    children: _jsxs(ModalContainer, {
      ref: modalRef,
      size: size,
      className: className,
      centered: centered,
      scrollable: scrollable,
      onClick: e => e.stopPropagation(),
      children: [
        (title || showCloseButton) &&
          _jsxs(ModalHeader, {
            children: [
              title && _jsx(ModalTitle, { children: title }),
              showCloseButton &&
                _jsx(CloseButton, { onClick: onClose, 'aria-label': 'Close', children: '\u00D7' }),
            ],
          }),
        _jsx(ModalContent, { scrollable: scrollable, children: children }),
        hasFooter &&
          (footer || primaryActionText || secondaryActionText) &&
          _jsx(ModalFooter, { children: footer || defaultFooter }),
      ],
    }),
  });
  // Use portal to render modal outside of the component hierarchy
  return createPortal(modalContent, document.body);
};
//# sourceMappingURL=Modal.js.map
