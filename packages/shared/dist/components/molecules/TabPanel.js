import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * TabPanel Component
 *
 * A reusable tab panel component for progressive disclosure UI.
 * MOVED TO SHARED: This component was being imported across multiple features.
 */
import { useState } from 'react';
import styled from 'styled-components';
const TabContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;
const TabList = styled.div`
  display: flex;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const TabButton = styled.button`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  background: none;
  border: none;
  border-bottom: 2px solid ${({ active, theme }) => (active ? theme.colors.primary : 'transparent')};
  color: ${({ active, theme }) => (active ? theme.colors.primary : theme.colors.textSecondary)};
  font-weight: ${({ active, theme }) =>
    active ? theme.fontWeights.semibold : theme.fontWeights.regular};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }

  &:focus {
    outline: none;
    color: ${({ theme }) => theme.colors.primary};
  }
`;
const TabContent = styled.div`
  padding: ${({ theme }) => theme.spacing.sm} 0;
`;
/**
 * TabPanel Component
 *
 * A reusable tab panel component for progressive disclosure UI.
 * Can be used in controlled or uncontrolled mode.
 */
const TabPanel = ({ tabs, defaultTab, className, activeTab: controlledActiveTab, onTabClick }) => {
  // Internal state for uncontrolled mode
  const [internalActiveTab, setInternalActiveTab] = useState(defaultTab || tabs[0].id);
  // Use controlled or uncontrolled active tab
  const activeTab = controlledActiveTab !== undefined ? controlledActiveTab : internalActiveTab;
  const handleTabChange = (e, tabId) => {
    // Prevent the default action (form submission)
    e.preventDefault();
    e.stopPropagation(); // Also stop propagation to prevent bubbling
    // If onTabClick is provided, call it (controlled mode)
    if (onTabClick) {
      onTabClick(tabId);
    } else {
      // Otherwise update internal state (uncontrolled mode)
      setInternalActiveTab(tabId);
    }
  };
  return _jsxs(TabContainer, {
    className: className,
    children: [
      _jsx(TabList, {
        children: tabs.map(tab =>
          _jsx(
            TabButton,
            {
              active: activeTab === tab.id,
              onClick: e => handleTabChange(e, tab.id),
              type: 'button', // Explicitly set type to button to prevent form submission
              form: '', // Disconnect from any form to prevent submission
              tabIndex: 0,
              'data-tab-id': tab.id,
              children: tab.label,
            },
            tab.id
          )
        ),
      }),
      _jsx(TabContent, { children: tabs.find(tab => tab.id === activeTab)?.content }),
    ],
  });
};
export default TabPanel;
//# sourceMappingURL=TabPanel.js.map
