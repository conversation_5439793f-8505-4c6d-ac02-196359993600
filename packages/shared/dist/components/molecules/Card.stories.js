import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from 'react/jsx-runtime';
import { Card } from './Card';
import { Button } from '../atoms/Button';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
  title: 'Molecules/Card',
  component: Card,
  tags: ['autodocs'],
  decorators: [
    Story =>
      _jsx(ThemeProvider, {
        children: _jsx('div', {
          style: { padding: '1rem', maxWidth: '600px' },
          children: _jsx(Story, {}),
        }),
      }),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'primary', 'secondary', 'outlined', 'elevated'],
    },
    padding: {
      control: 'select',
      options: ['none', 'small', 'medium', 'large'],
    },
    bordered: {
      control: 'boolean',
    },
    isLoading: {
      control: 'boolean',
    },
    hasError: {
      control: 'boolean',
    },
    clickable: {
      control: 'boolean',
    },
    onClick: { action: 'clicked' },
  },
};
export default meta;
export const Default = {
  args: {
    title: 'Card Title',
    children: _jsx('p', { children: 'This is a basic card with a title and content.' }),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const WithSubtitle = {
  args: {
    title: 'Card Title',
    subtitle: 'Card Subtitle',
    children: _jsx('p', { children: 'This card has both a title and a subtitle.' }),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const WithActions = {
  args: {
    title: 'Card with Actions',
    actions: _jsxs(_Fragment, {
      children: [
        _jsx(Button, { variant: 'outline', size: 'small', children: 'Cancel' }),
        _jsx(Button, { size: 'small', children: 'Save' }),
      ],
    }),
    children: _jsx('p', { children: 'This card has action buttons in the header.' }),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const WithFooter = {
  args: {
    title: 'Card with Footer',
    children: _jsx('p', { children: 'This card has a footer section.' }),
    footer: _jsxs('div', {
      style: { display: 'flex', justifyContent: 'flex-end', gap: '8px' },
      children: [
        _jsx(Button, { variant: 'outline', children: 'Cancel' }),
        _jsx(Button, { children: 'Submit' }),
      ],
    }),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const Loading = {
  args: {
    title: 'Loading Card',
    children: _jsx('p', { children: 'This content will be hidden while loading.' }),
    isLoading: true,
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const Error = {
  args: {
    title: 'Error Card',
    children: _jsx('p', { children: 'This content will be shown after the error message.' }),
    hasError: true,
    errorMessage: 'An error occurred while loading data.',
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const Clickable = {
  args: {
    title: 'Clickable Card',
    children: _jsx('p', { children: 'Click on this card to trigger an action.' }),
    clickable: true,
    onClick: () => alert('Card clicked!'),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};
export const Variants = {
  render: () =>
    _jsxs('div', {
      style: { display: 'flex', flexDirection: 'column', gap: '16px' },
      children: [
        _jsx(Card, {
          title: 'Default Variant',
          variant: 'default',
          children: _jsx('p', { children: 'Default card variant' }),
        }),
        _jsx(Card, {
          title: 'Primary Variant',
          variant: 'primary',
          children: _jsx('p', { children: 'Primary card variant' }),
        }),
        _jsx(Card, {
          title: 'Secondary Variant',
          variant: 'secondary',
          children: _jsx('p', { children: 'Secondary card variant' }),
        }),
        _jsx(Card, {
          title: 'Outlined Variant',
          variant: 'outlined',
          children: _jsx('p', { children: 'Outlined card variant' }),
        }),
        _jsx(Card, {
          title: 'Elevated Variant',
          variant: 'elevated',
          children: _jsx('p', { children: 'Elevated card variant' }),
        }),
      ],
    }),
};
//# sourceMappingURL=Card.stories.js.map
