import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled, { css } from 'styled-components';
// Padding styles
const paddingStyles = {
  none: css`
    padding: 0;
  `,
  small: css`
    padding: ${({ theme }) => theme.spacing.sm};
  `,
  medium: css`
    padding: ${({ theme }) => theme.spacing.md};
  `,
  large: css`
    padding: ${({ theme }) => theme.spacing.lg};
  `,
};
// Variant styles
const variantStyles = {
  default: css`
    background-color: ${({ theme }) => theme.colors.surface};
  `,
  primary: css`
    background-color: ${({ theme }) => theme.colors.primary}10;
    border-color: ${({ theme }) => theme.colors.primary}30;
  `,
  secondary: css`
    background-color: ${({ theme }) => theme.colors.secondary}10;
    border-color: ${({ theme }) => theme.colors.secondary}30;
  `,
  outlined: css`
    background-color: transparent;
    border: 1px solid ${({ theme }) => theme.colors.border};
  `,
  elevated: css`
    background-color: ${({ theme }) => theme.colors.surface};
    box-shadow: ${({ theme }) => theme.shadows.md};
    border: none;
  `,
};
const CardContainer = styled.div`
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  transition: all ${({ theme }) => theme.transitions.fast};
  position: relative;

  /* Border styles */
  ${({ bordered, theme }) =>
    bordered &&
    css`
      border: 1px solid ${theme.colors.border};
    `}

  /* Apply padding styles */
  ${({ padding }) => paddingStyles[padding]}

  /* Apply variant styles */
  ${({ variant }) => variantStyles[variant]}

  /* Clickable styles */
  ${({ clickable }) =>
    clickable &&
    css`
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: ${({ theme }) => theme.shadows.md};
      }

      &:active {
        transform: translateY(0);
      }
    `}
`;
const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const HeaderContent = styled.div`
  flex: 1;
`;
const CardTitle = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const CardSubtitle = styled.div`
  margin-top: ${({ theme }) => theme.spacing.xs};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const ActionsContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const CardContent = styled.div``;
const CardFooter = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;
const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => `${theme.colors.background}80`};
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
`;
const ErrorContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.error}10;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.error};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors.background};
  border-top: 3px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
/**
 * Card Component
 *
 * A customizable card component that follows the design system.
 */
export const Card = ({
  children,
  title = '',
  subtitle = '',
  bordered = true,
  variant = 'default',
  padding = 'medium',
  className = '',
  footer,
  actions,
  isLoading = false,
  hasError = false,
  errorMessage = 'An error occurred',
  clickable = false,
  onClick,
  ...rest
}) => {
  const hasHeader = title || subtitle || actions;
  return _jsxs(CardContainer, {
    bordered: bordered,
    variant: variant,
    padding: padding,
    clickable: clickable,
    className: className,
    onClick: clickable ? onClick : undefined,
    ...rest,
    children: [
      isLoading && _jsx(LoadingOverlay, { children: _jsx(LoadingSpinner, {}) }),
      hasHeader &&
        _jsxs(CardHeader, {
          children: [
            _jsxs(HeaderContent, {
              children: [
                title && _jsx(CardTitle, { children: title }),
                subtitle && _jsx(CardSubtitle, { children: subtitle }),
              ],
            }),
            actions && _jsx(ActionsContainer, { children: actions }),
          ],
        }),
      hasError && _jsx(ErrorContainer, { children: _jsx('p', { children: errorMessage }) }),
      _jsx(CardContent, { children: children }),
      footer && _jsx(CardFooter, { children: footer }),
    ],
  });
};
//# sourceMappingURL=Card.js.map
