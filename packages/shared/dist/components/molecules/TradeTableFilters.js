import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Input } from '../atoms/Input';
import { Select } from '../atoms/Select';
import { Button } from '../atoms/Button';
// Typed constants for field names
const TRADE_FILTER_FIELDS = {
    MODEL_TYPE: 'model_type',
    WIN_LOSS: 'win_loss',
    DATE_FROM: 'dateFrom',
    DATE_TO: 'dateTo',
    SESSION: 'session',
    DIRECTION: 'direction',
    MARKET: 'market',
    MIN_R_MULTIPLE: 'min_r_multiple',
    MAX_R_MULTIPLE: 'max_r_multiple',
    MIN_PATTERN_QUALITY: 'min_pattern_quality',
    MAX_PATTERN_QUALITY: 'max_pattern_quality',
};
const FiltersContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;
const FilterRow = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '12px'};
  align-items: end;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
const FilterGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
  min-width: 120px;
`;
const FilterLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;
const FilterActions = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '12px'};
  align-items: center;
  margin-left: auto;

  @media (max-width: 768px) {
    margin-left: 0;
    justify-content: flex-end;
  }
`;
const AdvancedFilters = styled.div `
  display: ${({ isVisible }) => (isVisible ? 'flex' : 'none')};
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
  padding-top: ${({ theme }) => theme.spacing?.md || '16px'};
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;
const RangeInputGroup = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
  align-items: center;
`;
const RangeLabel = styled.span `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;
/**
 * Trade Table Filters Component
 */
export const TradeTableFilters = ({ filters, onFiltersChange, onReset, isLoading = false, showAdvanced = false, onToggleAdvanced, }) => {
    const handleFilterChange = (key, value) => {
        onFiltersChange({
            ...filters,
            [key]: value,
        });
    };
    const handleReset = () => {
        const resetFilters = {};
        onFiltersChange(resetFilters);
        onReset?.();
    };
    const hasActiveFilters = Object.values(filters).some((value) => value !== undefined && value !== '' && value !== null);
    return (_jsxs(FiltersContainer, { children: [_jsxs(FilterRow, { children: [_jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Date From" }), _jsx(Input, { type: "date", value: filters.dateFrom || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.DATE_FROM, value), disabled: isLoading })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Date To" }), _jsx(Input, { type: "date", value: filters.dateTo || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.DATE_TO, value), disabled: isLoading })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Model Type" }), _jsx(Select, { options: [
                                    { value: '', label: 'All Models' },
                                    { value: 'RD-Cont', label: 'RD-Cont' },
                                    { value: 'FVG-RD', label: 'FVG-RD' },
                                    { value: 'True-RD', label: 'True-RD' },
                                    { value: 'IMM-RD', label: 'IMM-RD' },
                                    { value: 'Dispersed-RD', label: 'Dispersed-RD' },
                                    { value: 'Wide-Gap-RD', label: 'Wide-Gap-RD' },
                                ], value: filters.model_type || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.MODEL_TYPE, value), disabled: isLoading })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Session" }), _jsx(Select, { options: [
                                    { value: '', label: 'All Sessions' },
                                    { value: 'Pre-Market', label: 'Pre-Market' },
                                    { value: 'NY Open', label: 'NY Open' },
                                    { value: '10:50-11:10', label: '10:50-11:10' },
                                    { value: '11:50-12:10', label: '11:50-12:10' },
                                    { value: 'Lunch Macro', label: 'Lunch Macro' },
                                    { value: '13:50-14:10', label: '13:50-14:10' },
                                    { value: '14:50-15:10', label: '14:50-15:10' },
                                    { value: '15:15-15:45', label: '15:15-15:45' },
                                    { value: 'MOC', label: 'MOC' },
                                    { value: 'Post MOC', label: 'Post MOC' },
                                ], value: filters.session || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.SESSION, value), disabled: isLoading })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Direction" }), _jsx(Select, { options: [
                                    { value: '', label: 'All Directions' },
                                    { value: 'Long', label: 'Long' },
                                    { value: 'Short', label: 'Short' },
                                ], value: filters.direction || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.DIRECTION, value), disabled: isLoading })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Result" }), _jsx(Select, { options: [
                                    { value: '', label: 'All Results' },
                                    { value: 'Win', label: 'Win' },
                                    { value: 'Loss', label: 'Loss' },
                                ], value: filters.win_loss || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.WIN_LOSS, value), disabled: isLoading })] }), _jsxs(FilterActions, { children: [onToggleAdvanced && (_jsxs(Button, { variant: "outline", size: "small", onClick: onToggleAdvanced, disabled: isLoading, children: [showAdvanced ? 'Hide' : 'Show', " Advanced"] })), _jsx(Button, { variant: "outline", size: "small", onClick: handleReset, disabled: isLoading || !hasActiveFilters, children: "Reset" })] })] }), _jsx(AdvancedFilters, { isVisible: showAdvanced, children: _jsxs(FilterRow, { children: [_jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Market" }), _jsx(Select, { options: [
                                        { value: '', label: 'All Markets' },
                                        { value: 'MNQ', label: 'MNQ' },
                                        { value: 'NQ', label: 'NQ' },
                                        { value: 'ES', label: 'ES' },
                                        { value: 'MES', label: 'MES' },
                                        { value: 'YM', label: 'YM' },
                                        { value: 'MYM', label: 'MYM' },
                                    ], value: filters.market || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.MARKET, value), disabled: isLoading })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "R Multiple Range" }), _jsxs(RangeInputGroup, { children: [_jsx(Input, { type: "number", placeholder: "Min", step: "0.1", value: filters.min_r_multiple?.toString() || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.MIN_R_MULTIPLE, value ? Number(value) : undefined), disabled: isLoading, style: { width: '80px' } }), _jsx(RangeLabel, { children: "to" }), _jsx(Input, { type: "number", placeholder: "Max", step: "0.1", value: filters.max_r_multiple?.toString() || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.MAX_R_MULTIPLE, value ? Number(value) : undefined), disabled: isLoading, style: { width: '80px' } })] })] }), _jsxs(FilterGroup, { children: [_jsx(FilterLabel, { children: "Pattern Quality Range" }), _jsxs(RangeInputGroup, { children: [_jsx(Input, { type: "number", placeholder: "Min", min: "1", max: "5", step: "0.1", value: filters.min_pattern_quality?.toString() || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.MIN_PATTERN_QUALITY, value ? Number(value) : undefined), disabled: isLoading, style: { width: '80px' } }), _jsx(RangeLabel, { children: "to" }), _jsx(Input, { type: "number", placeholder: "Max", min: "1", max: "5", step: "0.1", value: filters.max_pattern_quality?.toString() || '', onChange: (value) => handleFilterChange(TRADE_FILTER_FIELDS.MAX_PATTERN_QUALITY, value ? Number(value) : undefined), disabled: isLoading, style: { width: '80px' } })] })] })] }) })] }));
};
//# sourceMappingURL=TradeTableFilters.js.map