import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Trade Table
 *
 * Main table component for displaying trading data with filtering and sorting.
 */
import { useMemo, useState } from 'react';
import styled, { css } from 'styled-components';
import { Button } from '../atoms/Button';
import {
  getTradeTableColumns,
  getCompactTradeTableColumns,
  getPerformanceTradeTableColumns,
} from './TradeTableColumns';
import { TradeTableRow } from './TradeTableRow';
import { TradeTableFilters } from './TradeTableFilters';
const TableContainer = styled.div`
  width: 100%;
  overflow: auto;
  ${({ height }) => height && `height: ${height};`}
  ${({ scrollable }) => scrollable && `overflow-x: auto;`}
`;
const StyledTable = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};

  ${({ bordered, theme }) =>
    bordered &&
    css`
      border: 1px solid ${theme.colors?.border || '#e5e7eb'};
      border-radius: ${theme.borderRadius?.sm || '4px'};
    `}

  ${({ compact, theme }) =>
    compact
      ? css`
          th,
          td {
            padding: ${theme.spacing?.xs || '8px'} ${theme.spacing?.sm || '12px'};
          }
        `
      : css`
          th,
          td {
            padding: ${theme.spacing?.sm || '12px'} ${theme.spacing?.md || '16px'};
          }
        `}
`;
const TableHeader = styled.thead`
  ${({ stickyHeader }) =>
    stickyHeader &&
    css`
      position: sticky;
      top: 0;
      z-index: 1;
    `}
`;
const TableHeaderRow = styled.tr`
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
`;
const TableHeaderCell = styled.th`
  text-align: ${({ align }) => align || 'left'};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
  white-space: nowrap;
  ${({ width }) => width && `width: ${width};`}

  ${({ sortable }) =>
    sortable &&
    css`
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'}aa;
      }
    `}

  ${({ isSorted, theme }) =>
    isSorted &&
    css`
      color: ${theme.colors?.primary || '#3b82f6'};
    `}
`;
const SortIcon = styled.span`
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing?.xs || '8px'};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : direction === 'desc' ? '↓' : '↕')}';
  }
`;
const TableBody = styled.tbody``;
const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;
const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => `${theme.colors?.background || '#ffffff'}80`};
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
`;
const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors?.background || '#f8f9fa'};
  border-top: 3px solid ${({ theme }) => theme.colors?.primary || '#3b82f6'};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
const PaginationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.md || '16px'} 0;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;
const PageInfo = styled.div`
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;
const PaginationControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
`;
/**
 * Trade Table Component
 */
export const TradeTable = ({
  data,
  isLoading = false,
  bordered = true,
  striped = true,
  hoverable = true,
  compact = false,
  stickyHeader = false,
  height = '',
  onRowClick,
  isRowSelected,
  onSort,
  sortColumn = '',
  sortDirection = 'asc',
  pagination = false,
  currentPage = 1,
  pageSize = 10,
  totalRows = 0,
  onPageChange,
  onPageSizeChange: _onPageSizeChange,
  className = '',
  emptyMessage = 'No trades available',
  scrollable = true,
  showFilters = false,
  filters = {},
  onFiltersChange,
  columnPreset = 'default',
  customColumns,
  expandableRows = false,
  renderExpandedContent,
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  // Get columns based on preset or custom
  const columns = useMemo(() => {
    if (customColumns) return customColumns;
    switch (columnPreset) {
      case 'compact':
        return getCompactTradeTableColumns();
      case 'performance':
        return getPerformanceTradeTableColumns();
      default:
        return getTradeTableColumns();
    }
  }, [customColumns, columnPreset]);
  // Filter out hidden columns
  const visibleColumns = useMemo(() => columns.filter(col => !col.hidden), [columns]);
  // Calculate pagination
  const totalPages = useMemo(() => Math.ceil(totalRows / pageSize), [totalRows, pageSize]);
  const paginatedData = useMemo(() => {
    if (!pagination) return data;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    // If we have totalRows, assume data is already paginated from the server
    if (totalRows > 0 && data.length <= pageSize) {
      return data;
    }
    return data.slice(startIndex, endIndex);
  }, [data, pagination, currentPage, pageSize, totalRows]);
  // Handle sort
  const handleSort = columnId => {
    if (!onSort) return;
    const newDirection = sortColumn === columnId && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(columnId, newDirection);
  };
  // Handle page change
  const handlePageChange = page => {
    if (page < 1 || page > totalPages || !onPageChange) return;
    onPageChange(page);
  };
  return _jsxs('div', {
    children: [
      showFilters &&
        onFiltersChange &&
        _jsx(TradeTableFilters, {
          filters: filters,
          onFiltersChange: onFiltersChange,
          isLoading: isLoading,
          showAdvanced: showAdvancedFilters,
          onToggleAdvanced: () => setShowAdvancedFilters(!showAdvancedFilters),
        }),
      _jsxs('div', {
        style: { position: 'relative' },
        children: [
          isLoading && _jsx(LoadingOverlay, { children: _jsx(LoadingSpinner, {}) }),
          _jsx(TableContainer, {
            height: height,
            scrollable: scrollable,
            children: _jsxs(StyledTable, {
              bordered: bordered,
              striped: striped,
              compact: compact,
              className: className,
              children: [
                _jsx(TableHeader, {
                  stickyHeader: stickyHeader,
                  children: _jsxs(TableHeaderRow, {
                    children: [
                      expandableRows && _jsx(TableHeaderCell, { width: '40px', align: 'center' }),
                      visibleColumns.map(column =>
                        _jsxs(
                          TableHeaderCell,
                          {
                            sortable: column.sortable,
                            isSorted: sortColumn === column.id,
                            align: column.align,
                            width: column.width,
                            onClick: () => column.sortable && handleSort(column.id),
                            children: [
                              column.header,
                              column.sortable &&
                                _jsx(SortIcon, {
                                  direction: sortColumn === column.id ? sortDirection : undefined,
                                }),
                            ],
                          },
                          column.id
                        )
                      ),
                    ],
                  }),
                }),
                _jsx(TableBody, {
                  children:
                    paginatedData.length > 0
                      ? paginatedData.map((trade, index) =>
                          _jsx(
                            TradeTableRow,
                            {
                              trade: trade,
                              index: index,
                              columns: visibleColumns,
                              isSelected: isRowSelected ? isRowSelected(trade, index) : false,
                              hoverable: hoverable,
                              striped: striped,
                              expandable: expandableRows,
                              onRowClick: onRowClick,
                              expandedContent: renderExpandedContent?.(trade),
                            },
                            trade.trade.id || index
                          )
                        )
                      : _jsx('tr', {
                          children: _jsx('td', {
                            colSpan: visibleColumns.length + (expandableRows ? 1 : 0),
                            children: _jsx(EmptyState, { children: emptyMessage }),
                          }),
                        }),
                }),
              ],
            }),
          }),
          pagination &&
            totalPages > 0 &&
            _jsxs(PaginationContainer, {
              children: [
                _jsxs(PageInfo, {
                  children: [
                    'Showing ',
                    Math.min((currentPage - 1) * pageSize + 1, totalRows),
                    ' to',
                    ' ',
                    Math.min(currentPage * pageSize, totalRows),
                    ' of ',
                    totalRows,
                    ' entries',
                  ],
                }),
                _jsxs(PaginationControls, {
                  children: [
                    _jsx(Button, {
                      size: 'small',
                      variant: 'outline',
                      onClick: () => handlePageChange(1),
                      disabled: currentPage === 1,
                      children: 'First',
                    }),
                    _jsx(Button, {
                      size: 'small',
                      variant: 'outline',
                      onClick: () => handlePageChange(currentPage - 1),
                      disabled: currentPage === 1,
                      children: 'Prev',
                    }),
                    _jsx(Button, {
                      size: 'small',
                      variant: 'outline',
                      onClick: () => handlePageChange(currentPage + 1),
                      disabled: currentPage === totalPages,
                      children: 'Next',
                    }),
                    _jsx(Button, {
                      size: 'small',
                      variant: 'outline',
                      onClick: () => handlePageChange(totalPages),
                      disabled: currentPage === totalPages,
                      children: 'Last',
                    }),
                  ],
                }),
              ],
            }),
        ],
      }),
    ],
  });
};
//# sourceMappingURL=TradeTable.js.map
