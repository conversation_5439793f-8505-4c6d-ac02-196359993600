import { jsx as _jsx } from 'react/jsx-runtime';
import styled from 'styled-components';
import { Badge } from '../atoms/Badge';
// Type-safe field accessors for TradeRecord
// type TradeRecordField = keyof TradeRecord; // Unused for now
// type TradeTableField = TradeRecordField | 'symbol' | 'setup' | 'analysis'; // Unused for now
// Column ID constants to prevent typos
export const TRADE_COLUMN_IDS = {
  DATE: 'date',
  SYMBOL: 'symbol',
  DIRECTION: 'direction',
  MODEL_TYPE: 'model_type',
  SESSION: 'session',
  ENTRY_PRICE: 'entry_price',
  EXIT_PRICE: 'exit_price',
  R_MULTIPLE: 'r_multiple',
  ACHIEVED_PL: 'achieved_pl',
  WIN_LOSS: 'win_loss',
  PATTERN_QUALITY: 'pattern_quality_rating',
  ENTRY_TIME: 'entry_time',
  EXIT_TIME: 'exit_time',
};
// Styled components for trade-specific formatting
const ProfitLossCell = styled.span`
  color: ${({ isProfit, theme }) =>
    isProfit ? theme.colors.success || '#10b981' : theme.colors.error || '#ef4444'};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
`;
const DirectionBadge = styled(Badge)`
  background-color: ${({ direction, theme }) =>
    direction === 'Long' ? theme.colors.success || '#10b981' : theme.colors.error || '#ef4444'};
  color: white;
`;
const QualityRating = styled.span`
  color: ${({ rating, theme }) => {
    if (rating >= 4) return theme.colors.success || '#10b981';
    if (rating >= 3) return theme.colors.warning || '#f59e0b';
    return theme.colors.error || '#ef4444';
  }};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
`;
const RMultipleCell = styled.span`
  color: ${({ rMultiple, theme }) =>
    rMultiple > 0 ? theme.colors.success || '#10b981' : theme.colors.error || '#ef4444'};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
`;
/**
 * Utility functions for formatting trade data
 */
const formatCurrencyLocal = value => {
  if (value === undefined || value === null) return '-';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(value);
};
// const formatPercentageLocal = (value: number | undefined): string => {
//   if (value === undefined || value === null) return '-';
//   return `${(value * 100).toFixed(2)}%`;
// }; // Unused for now
const formatDateLocal = dateString => {
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch {
    return dateString;
  }
};
export const formatTime = timeString => {
  if (!timeString) return '-';
  return timeString;
};
/**
 * Default column definitions for trade tables
 */
export const getTradeTableColumns = () => [
  {
    id: TRADE_COLUMN_IDS.DATE,
    header: 'Date',
    sortable: true,
    width: '100px',
    cell: row => formatDateLocal(row.trade[TRADE_COLUMN_IDS.DATE]),
  },
  {
    id: TRADE_COLUMN_IDS.SYMBOL,
    header: 'Symbol',
    sortable: true,
    width: '80px',
    cell: row => row.trade.market || 'MNQ',
  },
  {
    id: TRADE_COLUMN_IDS.DIRECTION,
    header: 'Direction',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: row =>
      _jsx(DirectionBadge, {
        direction: row.trade[TRADE_COLUMN_IDS.DIRECTION],
        size: 'small',
        children: row.trade[TRADE_COLUMN_IDS.DIRECTION],
      }),
  },
  {
    id: TRADE_COLUMN_IDS.MODEL_TYPE,
    header: 'Model',
    sortable: true,
    width: '120px',
    cell: row => row.trade[TRADE_COLUMN_IDS.MODEL_TYPE] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.SESSION,
    header: 'Session',
    sortable: true,
    width: '120px',
    cell: row => row.trade[TRADE_COLUMN_IDS.SESSION] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.ENTRY_PRICE,
    header: 'Entry',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: row => formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ENTRY_PRICE]),
  },
  {
    id: TRADE_COLUMN_IDS.EXIT_PRICE,
    header: 'Exit',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: row => formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.EXIT_PRICE]),
  },
  {
    id: TRADE_COLUMN_IDS.R_MULTIPLE,
    header: 'R Multiple',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: row =>
      _jsx(RMultipleCell, {
        rMultiple: row.trade[TRADE_COLUMN_IDS.R_MULTIPLE] || 0,
        children: row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]
          ? `${row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]?.toFixed(2)}R`
          : '-',
      }),
  },
  {
    id: TRADE_COLUMN_IDS.ACHIEVED_PL,
    header: 'P&L',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: row =>
      _jsx(ProfitLossCell, {
        isProfit: (row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL] || 0) > 0,
        children: formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL]),
      }),
  },
  {
    id: TRADE_COLUMN_IDS.WIN_LOSS,
    header: 'Result',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: row =>
      _jsx(Badge, {
        variant: row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win' ? 'success' : 'error',
        size: 'small',
        children: row.trade[TRADE_COLUMN_IDS.WIN_LOSS] || '-',
      }),
  },
  {
    id: TRADE_COLUMN_IDS.PATTERN_QUALITY,
    header: 'Quality',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: row =>
      _jsx(QualityRating, {
        rating: row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY] || 0,
        children: row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]
          ? `${row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]}/5`
          : '-',
      }),
  },
  {
    id: TRADE_COLUMN_IDS.ENTRY_TIME,
    header: 'Entry Time',
    sortable: true,
    width: '100px',
    align: 'center',
    cell: row => formatTime(row.trade[TRADE_COLUMN_IDS.ENTRY_TIME]),
  },
  {
    id: TRADE_COLUMN_IDS.EXIT_TIME,
    header: 'Exit Time',
    sortable: true,
    width: '100px',
    align: 'center',
    cell: row => formatTime(row.trade[TRADE_COLUMN_IDS.EXIT_TIME]),
  },
];
/**
 * Compact column definitions for smaller displays
 */
export const getCompactTradeTableColumns = () => [
  {
    id: TRADE_COLUMN_IDS.DATE,
    header: 'Date',
    sortable: true,
    width: '90px',
    cell: row => formatDateLocal(row.trade[TRADE_COLUMN_IDS.DATE]),
  },
  {
    id: TRADE_COLUMN_IDS.SYMBOL,
    header: 'Symbol',
    sortable: true,
    width: '60px',
    cell: row => row.trade.market || 'MNQ',
  },
  {
    id: TRADE_COLUMN_IDS.DIRECTION,
    header: 'Dir',
    sortable: true,
    width: '50px',
    align: 'center',
    cell: row =>
      _jsx(DirectionBadge, {
        direction: row.trade[TRADE_COLUMN_IDS.DIRECTION],
        size: 'small',
        children: row.trade[TRADE_COLUMN_IDS.DIRECTION].charAt(0),
      }),
  },
  {
    id: TRADE_COLUMN_IDS.R_MULTIPLE,
    header: 'R',
    sortable: true,
    width: '60px',
    align: 'right',
    cell: row =>
      _jsx(RMultipleCell, {
        rMultiple: row.trade[TRADE_COLUMN_IDS.R_MULTIPLE] || 0,
        children: row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]
          ? `${row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]?.toFixed(1)}R`
          : '-',
      }),
  },
  {
    id: TRADE_COLUMN_IDS.ACHIEVED_PL,
    header: 'P&L',
    sortable: true,
    width: '80px',
    align: 'right',
    cell: row =>
      _jsx(ProfitLossCell, {
        isProfit: (row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL] || 0) > 0,
        children: formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL]),
      }),
  },
  {
    id: TRADE_COLUMN_IDS.WIN_LOSS,
    header: 'Result',
    sortable: true,
    width: '60px',
    align: 'center',
    cell: row =>
      _jsx(Badge, {
        variant: row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win' ? 'success' : 'error',
        size: 'small',
        children:
          row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win'
            ? 'W'
            : row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Loss'
            ? 'L'
            : '-',
      }),
  },
];
/**
 * Performance-focused column definitions
 */
export const getPerformanceTradeTableColumns = () => [
  {
    id: TRADE_COLUMN_IDS.DATE,
    header: 'Date',
    sortable: true,
    width: '100px',
    cell: row => formatDateLocal(row.trade[TRADE_COLUMN_IDS.DATE]),
  },
  {
    id: TRADE_COLUMN_IDS.MODEL_TYPE,
    header: 'Model',
    sortable: true,
    width: '120px',
    cell: row => row.trade[TRADE_COLUMN_IDS.MODEL_TYPE] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.SESSION,
    header: 'Session',
    sortable: true,
    width: '120px',
    cell: row => row.trade[TRADE_COLUMN_IDS.SESSION] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.R_MULTIPLE,
    header: 'R Multiple',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: row =>
      _jsx(RMultipleCell, {
        rMultiple: row.trade[TRADE_COLUMN_IDS.R_MULTIPLE] || 0,
        children: row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]
          ? `${row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]?.toFixed(2)}R`
          : '-',
      }),
  },
  {
    id: TRADE_COLUMN_IDS.ACHIEVED_PL,
    header: 'P&L',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: row =>
      _jsx(ProfitLossCell, {
        isProfit: (row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL] || 0) > 0,
        children: formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL]),
      }),
  },
  {
    id: TRADE_COLUMN_IDS.PATTERN_QUALITY,
    header: 'Quality',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: row =>
      _jsx(QualityRating, {
        rating: row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY] || 0,
        children: row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]
          ? `${row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]}/5`
          : '-',
      }),
  },
  {
    id: TRADE_COLUMN_IDS.WIN_LOSS,
    header: 'Result',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: row =>
      _jsx(Badge, {
        variant: row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win' ? 'success' : 'error',
        size: 'small',
        children: row.trade[TRADE_COLUMN_IDS.WIN_LOSS] || '-',
      }),
  },
];
//# sourceMappingURL=TradeTableColumns.js.map
