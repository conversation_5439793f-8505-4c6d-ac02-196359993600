import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
/**
 * Error Boundary Component
 *
 * A React error boundary component that catches errors in its child component tree
 * and displays a fallback UI instead of crashing the entire application.
 *
 * This is a unified error boundary that can be used at any level of the application.
 */
import { Component } from 'react';
import styled from 'styled-components';
// Styled components
const ErrorContainer = styled.div`
  padding: 1.5rem;
  margin: ${props => (props.isAppLevel ? '0' : '1rem 0')};
  border-radius: 0.5rem;
  background-color: ${props => (props.isAppLevel ? '#1a1f2c' : '#f44336')};
  color: #ffffff;
  ${props =>
    props.isAppLevel &&
    `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `}
`;
const ErrorCard = styled.div`
  background-color: #252a37;
  border-radius: 0.5rem;
  padding: 2rem;
  width: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;
const ErrorTitle = styled.h3`
  margin-top: 0;
  font-size: ${props => (props.isAppLevel ? '1.5rem' : '1.25rem')};
  font-weight: 700;
  text-align: ${props => (props.isAppLevel ? 'center' : 'left')};
`;
const ErrorMessage = styled.p`
  margin-bottom: 1rem;
  text-align: ${props => (props.isAppLevel ? 'center' : 'left')};
`;
const ErrorDetails = styled.details`
  margin-bottom: 1rem;

  summary {
    cursor: pointer;
    color: #2196f3;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }
`;
const ErrorStack = styled.pre`
  font-size: 0.875rem;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow: auto;
  max-height: 200px;
`;
const ButtonContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
`;
const RetryButton = styled.button`
  background-color: #ffffff;
  color: #f44336;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }
`;
const SkipButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;
const ReloadButton = styled(RetryButton)`
  margin-top: 1rem;
  width: 100%;
`;
/**
 * Default fallback UI for the error boundary
 */
const DefaultFallback = ({ error, resetError, isAppLevel, name, onSkip }) => {
  const handleReload = () => {
    window.location.reload();
  };
  if (isAppLevel) {
    return _jsx(ErrorContainer, {
      isAppLevel: true,
      children: _jsxs(ErrorCard, {
        children: [
          _jsx(ErrorTitle, { isAppLevel: true, children: 'Something went wrong' }),
          _jsx(ErrorMessage, {
            isAppLevel: true,
            children:
              "We're sorry, but an unexpected error has occurred. Please try reloading the application.",
          }),
          _jsxs(ErrorDetails, {
            children: [
              _jsx('summary', { children: 'Technical Details' }),
              _jsx(ErrorMessage, { children: error.message }),
              error.stack && _jsx(ErrorStack, { children: error.stack }),
            ],
          }),
          _jsx(ReloadButton, { onClick: handleReload, children: 'Reload Application' }),
        ],
      }),
    });
  }
  return _jsxs(ErrorContainer, {
    children: [
      _jsx(ErrorTitle, { children: name ? `Error in ${name}` : 'Something went wrong' }),
      _jsx(ErrorMessage, {
        children: name
          ? `We encountered a problem while loading ${name}. You can try again${
              onSkip ? ' or skip this feature' : ''
            }.`
          : 'An unexpected error occurred. Please try again.',
      }),
      _jsxs(ErrorDetails, {
        children: [
          _jsx('summary', { children: 'Technical Details' }),
          _jsx(ErrorMessage, { children: error.message }),
          error.stack && _jsx(ErrorStack, { children: error.stack }),
        ],
      }),
      _jsxs(ButtonContainer, {
        children: [
          _jsx(RetryButton, { onClick: resetError, children: 'Try Again' }),
          onSkip && _jsx(SkipButton, { onClick: onSkip, children: 'Skip This Feature' }),
        ],
      }),
    ],
  });
};
/**
 * Error Boundary Component
 *
 * A unified React error boundary component that catches errors in its child component tree
 * and displays a fallback UI instead of crashing the entire application.
 *
 * This component can be used at both the application level and feature level.
 */
export class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }
  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }
  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    const { name } = this.props;
    const boundaryName = name ? `ErrorBoundary(${name})` : 'ErrorBoundary';
    console.error(`Error caught by ${boundaryName}:`, error, errorInfo);
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    // Here we could add Sentry integration
    // if (typeof window !== 'undefined' && window.Sentry) {
    //   window.Sentry.withScope((scope) => {
    //     scope.setTag('boundary', name || 'unnamed');
    //     scope.setExtra('componentStack', errorInfo.componentStack);
    //     window.Sentry.captureException(error);
    //   });
    // }
  }
  componentDidUpdate(prevProps) {
    // Reset the error state if the children prop changes and resetOnPropsChange is true
    if (
      this.state.hasError &&
      this.props.resetOnPropsChange &&
      prevProps.children !== this.props.children
    ) {
      this.resetError();
    }
  }
  componentWillUnmount() {
    // Reset the error state if resetOnUnmount is true
    if (this.state.hasError && this.props.resetOnUnmount) {
      this.resetError();
    }
  }
  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
    });
  };
  render() {
    const { hasError, error } = this.state;
    const { children, fallback, name, isFeatureBoundary, onSkip } = this.props;
    if (hasError && error) {
      // Render the fallback UI if an error occurred
      if (typeof fallback === 'function') {
        return fallback({ error, resetError: this.resetError });
      } else if (fallback) {
        return fallback;
      }
      // Use the default fallback
      return _jsx(DefaultFallback, {
        error: error,
        resetError: this.resetError,
        isAppLevel: !isFeatureBoundary,
        name: name,
        onSkip: onSkip,
      });
    }
    // Otherwise, render the children
    return children;
  }
}
export default ErrorBoundary;
//# sourceMappingURL=ErrorBoundary.js.map
