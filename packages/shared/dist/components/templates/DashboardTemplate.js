import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
const Container = styled.div`
  display: grid;
  grid-template-areas:
    'header header'
    'sidebar content';
  grid-template-columns: ${({ sidebarCollapsed }) => (sidebarCollapsed ? 'auto 1fr' : '240px 1fr')};
  grid-template-rows: auto 1fr;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  transition: grid-template-columns ${({ theme }) => theme.transitions.normal} ease;
`;
const HeaderContainer = styled.header`
  grid-area: header;
  background-color: ${({ theme }) => theme.colors.headerBackground};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding: ${({ theme }) => theme.spacing.md};
  z-index: ${({ theme }) => theme.zIndex.fixed};
`;
const SidebarContainer = styled.aside`
  grid-area: sidebar;
  background-color: ${({ theme }) => theme.colors.sidebarBackground};
  border-right: 1px solid ${({ theme }) => theme.colors.border};
  overflow-y: auto;
  transition: width ${({ theme }) => theme.transitions.normal} ease;
  width: ${({ collapsed }) => (collapsed ? '60px' : '240px')};
`;
const ContentContainer = styled.main`
  grid-area: content;
  overflow-y: auto;
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
`;
/**
 * Dashboard Template
 *
 * A template for dashboard pages with header, sidebar, and content areas.
 */
export const DashboardTemplate = ({
  header,
  sidebar,
  children,
  sidebarCollapsed = false,
  // toggleSidebar, // Unused prop
  className,
}) => {
  return _jsxs(Container, {
    sidebarCollapsed: sidebarCollapsed,
    className: className,
    children: [
      _jsx(HeaderContainer, { children: header }),
      _jsx(SidebarContainer, { collapsed: sidebarCollapsed, children: sidebar }),
      _jsx(ContentContainer, { children: children }),
    ],
  });
};
export default DashboardTemplate;
//# sourceMappingURL=DashboardTemplate.js.map
