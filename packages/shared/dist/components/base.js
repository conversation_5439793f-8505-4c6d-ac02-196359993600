import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
export const Button = ({
  children,
  variant = 'primary',
  disabled = false,
  loading = false,
  size = 'medium',
  onClick,
}) => {
  // Implementation will come later
  return _jsx('button', {
    className: `f1-button f1-button--${variant} f1-button--${size}`,
    disabled: disabled || loading,
    onClick: onClick,
    children: loading ? 'Loading...' : children,
  });
};
export const Card = ({ children, title, bordered = true }) => {
  // Implementation will come later
  return _jsxs('div', {
    className: `f1-card ${bordered ? 'f1-card--bordered' : ''}`,
    children: [
      title && _jsx('div', { className: 'f1-card__header', children: title }),
      _jsx('div', { className: 'f1-card__content', children: children }),
    ],
  });
};
export const Input = ({ value, onChange, placeholder, label, disabled = false, error }) => {
  // Implementation will come later
  return _jsxs('div', {
    className: 'f1-input-container',
    children: [
      label && _jsx('label', { className: 'f1-input__label', children: label }),
      _jsx('input', {
        className: `f1-input ${error ? 'f1-input--error' : ''}`,
        value: value,
        onChange: e => onChange(e.target.value),
        placeholder: placeholder,
        disabled: disabled,
      }),
      error && _jsx('div', { className: 'f1-input__error', children: error }),
    ],
  });
};
export const Select = ({ options, value, onChange, label, disabled = false, error }) => {
  // Implementation will come later
  return _jsxs('div', {
    className: 'f1-select-container',
    children: [
      label && _jsx('label', { className: 'f1-select__label', children: label }),
      _jsx('select', {
        className: `f1-select ${error ? 'f1-select--error' : ''}`,
        value: value,
        onChange: e => onChange(e.target.value),
        disabled: disabled,
        children: options.map(option =>
          _jsx('option', { value: option.value, children: option.label }, option.value)
        ),
      }),
      error && _jsx('div', { className: 'f1-select__error', children: error }),
    ],
  });
};
//# sourceMappingURL=base.js.map
