import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from 'react/jsx-runtime';
import styled, { css } from 'styled-components';
// Size styles
const sizeStyles = {
  small: css`
    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};
    font-size: ${({ theme }) => theme.fontSizes.xs};
    min-height: 20px;
    min-width: ${({ dot }) => (dot ? '8px' : '20px')};
  `,
  medium: css`
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    min-height: 24px;
    min-width: ${({ dot }) => (dot ? '10px' : '24px')};
  `,
  large: css`
    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
    font-size: ${({ theme }) => theme.fontSizes.md};
    min-height: 32px;
    min-width: ${({ dot }) => (dot ? '12px' : '32px')};
  `,
};
// Variant styles
const getVariantStyles = (variant, solid, outlined = false) => {
  return css`
    ${({ theme }) => {
      // Get the appropriate colors based on the variant
      let bgColor, textColor, borderColor;
      switch (variant) {
        case 'primary':
          bgColor = solid ? theme.colors.primary : `${theme.colors.primary}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.primary;
          borderColor = theme.colors.primary;
          break;
        case 'secondary':
          bgColor = solid ? theme.colors.secondary : `${theme.colors.secondary}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.secondary;
          borderColor = theme.colors.secondary;
          break;
        case 'success':
          bgColor = solid ? theme.colors.success : `${theme.colors.success}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.success;
          borderColor = theme.colors.success;
          break;
        case 'warning':
          bgColor = solid ? theme.colors.warning : `${theme.colors.warning}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.warning;
          borderColor = theme.colors.warning;
          break;
        case 'error':
          bgColor = solid ? theme.colors.error : `${theme.colors.error}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.error;
          borderColor = theme.colors.error;
          break;
        case 'info':
          bgColor = solid ? theme.colors.info : `${theme.colors.info}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.info;
          borderColor = theme.colors.info;
          break;
        case 'neutral':
          bgColor = solid ? theme.colors.textSecondary : `${theme.colors.textSecondary}10`;
          textColor = solid ? theme.colors.textInverse : theme.colors.textSecondary;
          borderColor = theme.colors.textSecondary;
          break;
        default: // 'default'
          bgColor = solid ? theme.colors.textSecondary : `${theme.colors.textSecondary}20`;
          textColor = solid ? theme.colors.textInverse : theme.colors.textSecondary;
          borderColor = theme.colors.textSecondary;
      }
      if (outlined) {
        return `
          background-color: transparent;
          color: ${borderColor};
          border: 1px solid ${borderColor};
        `;
      }
      return `
        background-color: ${bgColor};
        color: ${textColor};
        border: 1px solid transparent;
      `;
    }}
  `;
};
const IconContainer = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
`;
const StartIcon = styled(IconContainer)`
  margin-right: ${({ theme }) => theme.spacing.xxs};
`;
const EndIcon = styled(IconContainer)`
  margin-left: ${({ theme }) => theme.spacing.xxs};
`;
const StyledBadge = styled.span`
  display: ${({ inline }) => (inline ? 'inline-flex' : 'flex')};
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme, rounded, dot }) =>
    dot ? '50%' : rounded ? '9999px' : theme.borderRadius.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  white-space: nowrap;

  /* Apply size styles */
  ${({ size }) => sizeStyles[size]}

  /* Apply variant styles */
  ${({ variant, solid, outlined }) => getVariantStyles(variant, solid, outlined || false)}

  /* Dot style */
  ${({ dot }) =>
    dot &&
    css`
      padding: 0;
      height: 8px;
      width: 8px;
    `}

  /* Counter style */
  ${({ counter }) =>
    counter &&
    css`
      min-width: 1.5em;
      height: 1.5em;
      padding: 0 0.5em;
      border-radius: 1em;
    `}

  /* Clickable styles */
  ${({ clickable }) =>
    clickable &&
    css`
      cursor: pointer;
      transition: opacity ${({ theme }) => theme.transitions.fast};

      &:hover {
        opacity: 0.8;
      }

      &:active {
        opacity: 0.6;
      }
    `}
`;
/**
 * Badge Component
 *
 * A customizable badge component for displaying status, labels, or counts.
 */
export const Badge = ({
  children,
  variant = 'default',
  size = 'medium',
  solid = false,
  className = '',
  style,
  onClick,
  rounded = false,
  dot = false,
  counter = false,
  outlined = false,
  startIcon,
  endIcon,
  max,
  inline = true,
}) => {
  // Format content for counter badges with max value
  let content = children;
  if (counter && typeof children === 'number' && max !== undefined && children > max) {
    content = `${max}+`;
  }
  return _jsx(StyledBadge, {
    variant: variant,
    size: size,
    solid: solid,
    clickable: !!onClick,
    className: className,
    style: style,
    onClick: onClick,
    rounded: rounded,
    dot: dot,
    counter: counter,
    outlined: outlined,
    inline: inline,
    children:
      !dot &&
      _jsxs(_Fragment, {
        children: [
          startIcon && _jsx(StartIcon, { children: startIcon }),
          content,
          endIcon && _jsx(EndIcon, { children: endIcon }),
        ],
      }),
  });
};
//# sourceMappingURL=Badge.js.map
