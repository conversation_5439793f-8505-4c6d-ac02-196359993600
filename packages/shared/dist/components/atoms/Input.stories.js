import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { useState } from 'react';
import { Input } from './Input';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
  title: 'Atoms/Input',
  component: Input,
  tags: ['autodocs'],
  decorators: [
    Story =>
      _jsx(ThemeProvider, {
        children: _jsx('div', {
          style: { padding: '1rem', maxWidth: '400px' },
          children: _jsx(Story, {}),
        }),
      }),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'password', 'email', 'number', 'tel', 'url'],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      control: 'boolean',
    },
    required: {
      control: 'boolean',
    },
    clearable: {
      control: 'boolean',
    },
    success: {
      control: 'boolean',
    },
    loading: {
      control: 'boolean',
    },
    fullWidth: {
      control: 'boolean',
    },
    showCharCount: {
      control: 'boolean',
    },
    onChange: { action: 'changed' },
    onClear: { action: 'cleared' },
  },
};
export default meta;
// Controlled component wrapper for interactive stories
const ControlledInput = args => {
  const [value, setValue] = useState(args.value || '');
  return _jsx(Input, { ...args, value: value, onChange: setValue });
};
export const Default = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    placeholder: 'Enter text',
    value: '',
  },
};
export const WithLabel = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Username',
    placeholder: 'Enter username',
    value: '',
  },
};
export const WithHelperText = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    type: 'password',
    helperText: 'Password must be at least 8 characters',
    value: '',
  },
};
export const WithError = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    type: 'email',
    error: 'Please enter a valid email address',
    value: 'invalid-email',
  },
};
export const WithSuccess = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    type: 'email',
    success: true,
    value: '<EMAIL>',
  },
};
export const Disabled = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Disabled Input',
    placeholder: 'This input is disabled',
    disabled: true,
    value: '',
  },
};
export const Required = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Required Field',
    placeholder: 'This field is required',
    required: true,
    value: '',
  },
};
export const WithIcons = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Search',
    placeholder: 'Search...',
    startIcon: _jsx('span', { children: '\uD83D\uDD0D' }),
    endIcon: _jsx('span', { children: '\u2318K' }),
    value: '',
  },
};
export const Clearable = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Clearable Input',
    placeholder: 'Type something...',
    clearable: true,
    value: 'This can be cleared',
  },
};
export const WithCharCount = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Tweet',
    placeholder: "What's happening?",
    maxLength: 280,
    showCharCount: true,
    value: 'This is a sample tweet with character count.',
  },
};
export const Small = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Small Input',
    placeholder: 'Small size',
    size: 'small',
    value: '',
  },
};
export const Large = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Large Input',
    placeholder: 'Large size',
    size: 'large',
    value: '',
  },
};
export const FullWidth = {
  render: args => _jsx(ControlledInput, { ...args }),
  args: {
    label: 'Full Width Input',
    placeholder: 'This input takes full width',
    fullWidth: true,
    value: '',
  },
};
export const AllVariants = {
  render: () =>
    _jsxs('div', {
      style: { display: 'flex', flexDirection: 'column', gap: '16px' },
      children: [
        _jsx(Input, {
          label: 'Default Input',
          placeholder: 'Default input',
          value: '',
          onChange: () => {},
        }),
        _jsx(Input, {
          label: 'With Helper Text',
          placeholder: 'With helper text',
          helperText: 'This is a helper text',
          value: '',
          onChange: () => {},
        }),
        _jsx(Input, {
          label: 'With Error',
          placeholder: 'With error',
          error: 'This field has an error',
          value: 'Invalid value',
          onChange: () => {},
        }),
        _jsx(Input, {
          label: 'With Success',
          placeholder: 'With success',
          success: true,
          value: 'Valid value',
          onChange: () => {},
        }),
        _jsx(Input, {
          label: 'With Icons',
          placeholder: 'With icons',
          startIcon: _jsx('span', { children: '\uD83D\uDD0D' }),
          endIcon: _jsx('span', { children: '\u2318K' }),
          value: '',
          onChange: () => {},
        }),
      ],
    }),
};
//# sourceMappingURL=Input.stories.js.map
