import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled, { css, keyframes } from 'styled-components';
const spin = keyframes `
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;
const LoadingSpinner = styled.div `
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: ${spin} 0.8s linear infinite;
  margin-right: ${({ theme }) => theme.spacing.xs};
`;
// Size styles
const sizeStyles = {
    small: css `
    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.sm}`};
    font-size: ${({ theme }) => theme.fontSizes.xs};
    min-height: 32px;
  `,
    medium: css `
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.md}`};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    min-height: 40px;
  `,
    large: css `
    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.lg}`};
    font-size: ${({ theme }) => theme.fontSizes.md};
    min-height: 48px;
  `,
};
// Variant styles
const variantStyles = {
    primary: css `
    background-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.textPrimary || theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primaryDark};
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primaryDark};
      transform: translateY(0);
      box-shadow: none;
    }
  `,
    secondary: css `
    background-color: ${({ theme }) => theme.colors.secondary};
    color: ${({ theme }) => theme.colors.textPrimary || theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.secondaryDark};
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.secondaryDark};
      transform: translateY(0);
      box-shadow: none;
    }
  `,
    outline: css `
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary};
    border: 1px solid ${({ theme }) => theme.colors.primary};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}0d; /* 5% opacity */
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}1a; /* 10% opacity */
      transform: translateY(0);
    }
  `,
    text: css `
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary};
    border: none;
    padding-left: ${({ theme }) => theme.spacing.xs};
    padding-right: ${({ theme }) => theme.spacing.xs};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}0d; /* 5% opacity */
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}1a; /* 10% opacity */
    }
  `,
    success: css `
    background-color: ${({ theme }) => theme.colors.success};
    color: ${({ theme }) => theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.success}dd;
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: none;
    }
  `,
    danger: css `
    background-color: ${({ theme }) => theme.colors.error};
    color: ${({ theme }) => theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.error}dd;
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: none;
    }
  `,
};
const StyledButton = styled.button `
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};
  position: relative;
  overflow: hidden;

  /* Apply size styles */
  ${({ size = 'medium' }) => sizeStyles[size]}

  /* Apply variant styles */
  ${({ variant = 'primary' }) => variantStyles[variant]}

  /* Full width style */
  ${({ fullWidth }) => fullWidth &&
    css `
      width: 100%;
    `}

  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
    transform: translateY(0);
  }

  /* Icon spacing */
  ${({ $hasStartIcon }) => $hasStartIcon &&
    css `
      & > *:first-child {
        margin-right: ${({ theme }) => theme.spacing.xs};
      }
    `}

  ${({ $hasEndIcon }) => $hasEndIcon &&
    css `
      & > *:last-child {
        margin-left: ${({ theme }) => theme.spacing.xs};
      }
    `}
`;
const ButtonContent = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
`;
/**
 * Button Component
 *
 * A customizable button component that follows the design system.
 */
export const Button = ({ children, variant = 'primary', disabled = false, loading = false, size = 'medium', fullWidth = false, startIcon, endIcon, onClick, className = '', type = 'button', ...rest }) => {
    return (_jsx(StyledButton, { variant: variant, disabled: disabled || loading, size: size, fullWidth: fullWidth, onClick: onClick, className: className, type: type, "$hasStartIcon": !!startIcon && !loading, "$hasEndIcon": !!endIcon && !loading, ...rest, children: _jsxs(ButtonContent, { children: [loading && _jsx(LoadingSpinner, {}), !loading && startIcon, children, !loading && endIcon] }) }));
};
//# sourceMappingURL=Button.js.map