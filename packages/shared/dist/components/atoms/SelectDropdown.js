import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
const SelectContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: border-color ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.chartGrid};
    cursor: not-allowed;
  }
`;
/**
 * SelectDropdown Component
 *
 * A reusable dropdown component for selecting from a list of options.
 */
const SelectDropdown = ({
  id,
  name,
  value,
  onChange,
  options,
  label,
  required = false,
  disabled = false,
  className,
  placeholder,
}) => {
  return _jsxs(SelectContainer, {
    className: className,
    children: [
      label &&
        _jsxs(Label, {
          htmlFor: id,
          children: [label, required && _jsx('span', { style: { color: 'red' }, children: ' *' })],
        }),
      _jsxs(Select, {
        id: id,
        name: name,
        value: value,
        onChange: onChange,
        required: required,
        disabled: disabled,
        children: [
          placeholder && _jsx('option', { value: '', disabled: true, children: placeholder }),
          options.map(option =>
            _jsx('option', { value: option.value, children: option.label }, option.value)
          ),
        ],
      }),
    ],
  });
};
export default SelectDropdown;
//# sourceMappingURL=SelectDropdown.js.map
