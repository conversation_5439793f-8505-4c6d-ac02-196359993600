import { jsx as _jsx } from 'react/jsx-runtime';
import styled, { keyframes, css } from 'styled-components';
// F1-inspired spinning animation
const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;
// F1 racing stripes animation
const racingStripes = keyframes`
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
`;
const StyledSpinner = styled.div`
  display: inline-block;
  position: relative;

  ${({ $size }) => {
    switch ($size) {
      case 'xs':
        return css`
          width: 16px;
          height: 16px;
        `;
      case 'sm':
        return css`
          width: 20px;
          height: 20px;
        `;
      case 'md':
        return css`
          width: 32px;
          height: 32px;
        `;
      case 'lg':
        return css`
          width: 48px;
          height: 48px;
        `;
      case 'xl':
        return css`
          width: 64px;
          height: 64px;
        `;
      default:
        return css`
          width: 32px;
          height: 32px;
        `;
    }
  }}

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    border: 2px solid transparent;

    ${({ $variant, theme }) => {
      switch ($variant) {
        case 'primary':
          return css`
            border-top-color: ${theme.colors?.primary || '#dc2626'};
            border-right-color: ${theme.colors?.primary || '#dc2626'};
          `;
        case 'secondary':
          return css`
            border-top-color: ${theme.colors?.textSecondary || '#9ca3af'};
            border-right-color: ${theme.colors?.textSecondary || '#9ca3af'};
          `;
        case 'white':
          return css`
            border-top-color: #ffffff;
            border-right-color: #ffffff;
          `;
        case 'red':
          return css`
            border-top-color: #dc2626;
            border-right-color: #dc2626;
          `;
        default:
          return css`
            border-top-color: ${theme.colors?.primary || '#dc2626'};
            border-right-color: ${theme.colors?.primary || '#dc2626'};
          `;
      }
    }}

    animation: ${spin} ${({ $speed }) => 1 / $speed}s linear infinite;
  }

  ${({ $showStripes, $variant }) =>
    $showStripes &&
    css`
      &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        border-radius: 50%;
        background: ${$variant === 'red' || $variant === 'primary'
          ? 'linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)'
          : 'linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)'};
        background-size: 8px 8px;
        animation: ${racingStripes} ${props => 2 / props.$speed}s linear infinite;
      }
    `}
`;
const SpinnerContainer = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
`;
/**
 * LoadingSpinner Component
 *
 * A standardized loading spinner with F1 racing theme integration.
 * Provides consistent loading animations across the application.
 *
 * @example
 * ```typescript
 * // Basic usage
 * <LoadingSpinner />
 *
 * // Large red spinner with racing stripes
 * <LoadingSpinner
 *   size="lg"
 *   variant="red"
 *   showStripes={true}
 *   speed={1.5}
 * />
 *
 * // Small secondary spinner
 * <LoadingSpinner size="sm" variant="secondary" />
 * ```
 */
export const LoadingSpinner = props => {
  const {
    size = 'md',
    variant = 'primary',
    className,
    'aria-label': ariaLabel,
    speed = 1,
    showStripes = false,
  } = props;
  return _jsx(SpinnerContainer, {
    className: className,
    children: _jsx(StyledSpinner, {
      $size: size,
      $variant: variant,
      $speed: speed,
      $showStripes: showStripes,
      role: 'status',
      'aria-label': ariaLabel || 'Loading',
      'aria-live': 'polite',
    }),
  });
};
export default LoadingSpinner;
//# sourceMappingURL=LoadingSpinner.js.map
