import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { Tag } from './Tag';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
  title: 'Atoms/Tag',
  component: Tag,
  tags: ['autodocs'],
  decorators: [Story => _jsx(ThemeProvider, { children: _jsx(Story, {}) })],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'primary', 'secondary', 'success', 'warning', 'error', 'info'],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    removable: {
      control: 'boolean',
    },
    onClick: { action: 'clicked' },
    onRemove: { action: 'removed' },
  },
};
export default meta;
export const Default = {
  args: {
    children: 'Default Tag',
    variant: 'default',
    size: 'medium',
    removable: false,
  },
};
export const Primary = {
  args: {
    children: 'Primary Tag',
    variant: 'primary',
    size: 'medium',
    removable: false,
  },
};
export const Success = {
  args: {
    children: 'Success Tag',
    variant: 'success',
    size: 'medium',
    removable: false,
  },
};
export const Warning = {
  args: {
    children: 'Warning Tag',
    variant: 'warning',
    size: 'medium',
    removable: false,
  },
};
export const Error = {
  args: {
    children: 'Error Tag',
    variant: 'error',
    size: 'medium',
    removable: false,
  },
};
export const Removable = {
  args: {
    children: 'Removable Tag',
    variant: 'primary',
    size: 'medium',
    removable: true,
    onRemove: () => console.log('Tag removed'),
  },
};
export const Small = {
  args: {
    children: 'Small Tag',
    variant: 'primary',
    size: 'small',
    removable: false,
  },
};
export const Large = {
  args: {
    children: 'Large Tag',
    variant: 'primary',
    size: 'large',
    removable: false,
  },
};
export const Clickable = {
  args: {
    children: 'Clickable Tag',
    variant: 'primary',
    size: 'medium',
    removable: false,
    onClick: () => alert('Tag clicked!'),
  },
};
export const AllVariants = {
  render: () =>
    _jsxs('div', {
      style: { display: 'flex', gap: '8px', flexWrap: 'wrap' },
      children: [
        _jsx(Tag, { variant: 'default', children: 'Default' }),
        _jsx(Tag, { variant: 'primary', children: 'Primary' }),
        _jsx(Tag, { variant: 'secondary', children: 'Secondary' }),
        _jsx(Tag, { variant: 'success', children: 'Success' }),
        _jsx(Tag, { variant: 'warning', children: 'Warning' }),
        _jsx(Tag, { variant: 'error', children: 'Error' }),
        _jsx(Tag, { variant: 'info', children: 'Info' }),
      ],
    }),
};
export const RemovableTags = {
  render: () =>
    _jsxs('div', {
      style: { display: 'flex', gap: '8px', flexWrap: 'wrap' },
      children: [
        _jsx(Tag, {
          variant: 'default',
          removable: true,
          onRemove: () => console.log('Default removed'),
          children: 'Default',
        }),
        _jsx(Tag, {
          variant: 'primary',
          removable: true,
          onRemove: () => console.log('Primary removed'),
          children: 'Primary',
        }),
        _jsx(Tag, {
          variant: 'secondary',
          removable: true,
          onRemove: () => console.log('Secondary removed'),
          children: 'Secondary',
        }),
        _jsx(Tag, {
          variant: 'success',
          removable: true,
          onRemove: () => console.log('Success removed'),
          children: 'Success',
        }),
        _jsx(Tag, {
          variant: 'warning',
          removable: true,
          onRemove: () => console.log('Warning removed'),
          children: 'Warning',
        }),
        _jsx(Tag, {
          variant: 'error',
          removable: true,
          onRemove: () => console.log('Error removed'),
          children: 'Error',
        }),
        _jsx(Tag, {
          variant: 'info',
          removable: true,
          onRemove: () => console.log('Info removed'),
          children: 'Info',
        }),
      ],
    }),
};
//# sourceMappingURL=Tag.stories.js.map
