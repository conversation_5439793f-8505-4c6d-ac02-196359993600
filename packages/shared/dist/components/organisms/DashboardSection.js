import { jsxs as _jsxs, jsx as _jsx } from 'react/jsx-runtime';
/**
 * Dashboard Section Component
 *
 * A composable dashboard section that can render different types of content
 * based on the section name. This implements the composition pattern to
 * reduce coupling between dashboard components.
 */
import React from 'react';
import styled from 'styled-components';
const SectionContainer = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  padding-bottom: ${({ theme }) => theme.spacing.sm};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const SectionTitle = styled.h2`
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  margin: 0;
`;
const SectionActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;
const SectionContent = styled.div`
  min-height: 200px;
`;
const LoadingState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const ErrorState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: ${({ theme }) => theme.colors.danger};
  text-align: center;
`;
const DashboardSection = ({
  name,
  title,
  children,
  actions,
  isLoading = false,
  error = null,
  className,
  collapsible = false,
  defaultCollapsed = false,
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);
  const handleToggleCollapse = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };
  const displayTitle = title || name.charAt(0).toUpperCase() + name.slice(1);
  const renderContent = () => {
    if (error) {
      return _jsx(ErrorState, {
        children: _jsxs('div', {
          children: [
            _jsxs('div', { children: ['Error loading ', name] }),
            _jsx('div', { style: { fontSize: '0.9em', marginTop: '8px' }, children: error }),
          ],
        }),
      });
    }
    if (isLoading) {
      return _jsxs(LoadingState, { children: ['Loading ', name, '...'] });
    }
    if (!children) {
      return _jsxs(LoadingState, { children: ['No ', name, ' data available'] });
    }
    return children;
  };
  return _jsxs(SectionContainer, {
    className: className,
    'data-section': name,
    children: [
      _jsxs(SectionHeader, {
        children: [
          _jsxs(SectionTitle, {
            onClick: handleToggleCollapse,
            style: { cursor: collapsible ? 'pointer' : 'default' },
            children: [
              displayTitle,
              collapsible &&
                _jsx('span', {
                  style: { marginLeft: '8px', fontSize: '0.8em' },
                  children: isCollapsed ? '▶' : '▼',
                }),
            ],
          }),
          actions && _jsx(SectionActions, { children: actions }),
        ],
      }),
      !isCollapsed && _jsx(SectionContent, { children: renderContent() }),
    ],
  });
};
export default DashboardSection;
//# sourceMappingURL=DashboardSection.js.map
