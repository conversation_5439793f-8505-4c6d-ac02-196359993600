import { jsx as _jsx } from 'react/jsx-runtime';
import styled from 'styled-components';
import { Card } from '../molecules/Card';
// Button import removed as it's not used
import { LoadingPlaceholder } from '../atoms/LoadingPlaceholder';
import { EmptyState } from '../molecules/EmptyState';
const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;
/**
 * DataCard Component
 *
 * A specialized card component for displaying data sections with loading and error states.
 */
export const DataCard = ({
  title,
  children,
  isLoading = false,
  hasError = false,
  errorMessage = 'An error occurred while loading data',
  showRetry = true,
  onRetry,
  isEmpty = false,
  emptyMessage = 'No data available',
  emptyActionText,
  onEmptyAction,
  actionButton,
  className,
  ...cardProps
}) => {
  // Create actions for the card header
  const headerActions = _jsx(HeaderActions, { children: actionButton });
  // Determine what content to show based on state
  let content;
  if (isLoading) {
    content = _jsx(LoadingPlaceholder, { variant: 'card', text: 'Loading data...' });
  } else if (hasError) {
    content = _jsx(EmptyState, {
      title: 'Error',
      description: errorMessage,
      variant: 'compact',
      actionText: showRetry ? 'Retry' : undefined,
      onAction: showRetry ? onRetry : undefined,
    });
  } else if (isEmpty) {
    content = _jsx(EmptyState, {
      title: 'No Data',
      description: emptyMessage,
      variant: 'compact',
      actionText: emptyActionText,
      onAction: onEmptyAction,
    });
  } else {
    content = children;
  }
  return _jsx(Card, {
    title: title,
    actions: headerActions,
    className: className,
    ...cardProps,
    children: content,
  });
};
//# sourceMappingURL=DataCard.js.map
