import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import { DataCard } from './DataCard';
import { Button } from '../atoms/Button';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
  title: 'Organisms/DataCard',
  component: DataCard,
  tags: ['autodocs'],
  decorators: [
    Story =>
      _jsx(ThemeProvider, {
        children: _jsx('div', {
          style: { padding: '1rem', maxWidth: '600px' },
          children: _jsx(Story, {}),
        }),
      }),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    isLoading: {
      control: 'boolean',
    },
    hasError: {
      control: 'boolean',
    },
    isEmpty: {
      control: 'boolean',
    },
    showRetry: {
      control: 'boolean',
    },
    onRetry: { action: 'retry clicked' },
    onEmptyAction: { action: 'empty action clicked' },
  },
};
export default meta;
export const Default = {
  args: {
    title: 'Market Data',
    children: _jsxs('div', {
      children: [
        _jsx('p', { children: 'This is a data card with sample content.' }),
        _jsxs('ul', {
          children: [
            _jsx('li', { children: 'S&P 500: 4,587.64 (+0.37%)' }),
            _jsx('li', { children: 'Nasdaq: 14,346.02 (+0.54%)' }),
            _jsx('li', { children: 'Dow Jones: 36,142.22 (+0.15%)' }),
          ],
        }),
      ],
    }),
  },
};
export const WithActionButton = {
  args: {
    title: 'Market Data',
    actionButton: _jsx(Button, { size: 'small', children: 'Refresh' }),
    children: _jsxs('div', {
      children: [
        _jsx('p', { children: 'This data card has an action button in the header.' }),
        _jsxs('ul', {
          children: [
            _jsx('li', { children: 'S&P 500: 4,587.64 (+0.37%)' }),
            _jsx('li', { children: 'Nasdaq: 14,346.02 (+0.54%)' }),
            _jsx('li', { children: 'Dow Jones: 36,142.22 (+0.15%)' }),
          ],
        }),
      ],
    }),
  },
};
export const Loading = {
  args: {
    title: 'Market Data',
    isLoading: true,
    children: _jsx('div', {
      children: _jsx('p', { children: 'This content will be hidden while loading.' }),
    }),
  },
};
export const Error = {
  args: {
    title: 'Market Data',
    hasError: true,
    errorMessage: 'Failed to fetch market data. Please try again.',
    showRetry: true,
    onRetry: () => console.log('Retry clicked'),
    children: _jsx('div', {
      children: _jsx('p', { children: "This content will be hidden when there's an error." }),
    }),
  },
};
export const Empty = {
  args: {
    title: 'Market Data',
    isEmpty: true,
    emptyMessage: 'No market data available for the selected period.',
    emptyActionText: 'Change Filters',
    onEmptyAction: () => console.log('Empty action clicked'),
    children: _jsx('div', {
      children: _jsx('p', { children: 'This content will be hidden when the data is empty.' }),
    }),
  },
};
export const ErrorWithoutRetry = {
  args: {
    title: 'Market Data',
    hasError: true,
    errorMessage: 'Failed to fetch market data.',
    showRetry: false,
    children: _jsx('div', {
      children: _jsx('p', { children: "This content will be hidden when there's an error." }),
    }),
  },
};
export const AllStates = {
  render: () =>
    _jsxs('div', {
      style: { display: 'flex', flexDirection: 'column', gap: '16px' },
      children: [
        _jsx(DataCard, {
          title: 'Normal State',
          children: _jsx('p', { children: 'This is the normal state of a data card.' }),
        }),
        _jsx(DataCard, {
          title: 'Loading State',
          isLoading: true,
          children: _jsx('p', { children: 'This content is hidden while loading.' }),
        }),
        _jsx(DataCard, {
          title: 'Error State',
          hasError: true,
          errorMessage: 'An error occurred while fetching data.',
          showRetry: true,
          onRetry: () => console.log('Retry clicked'),
          children: _jsx('p', { children: "This content is hidden when there's an error." }),
        }),
        _jsx(DataCard, {
          title: 'Empty State',
          isEmpty: true,
          emptyMessage: 'No data available for the selected filters.',
          emptyActionText: 'Clear Filters',
          onEmptyAction: () => console.log('Empty action clicked'),
          children: _jsx('p', { children: 'This content is hidden when the data is empty.' }),
        }),
      ],
    }),
};
//# sourceMappingURL=DataCard.stories.js.map
