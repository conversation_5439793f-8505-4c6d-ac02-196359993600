/**
 * Trading Data Types
 *
 * This module exports type definitions for trading data.
 */
/**
 * Trade Direction
 */
export var TradeDirection;
(function (TradeDirection) {
  TradeDirection['LONG'] = 'LONG';
  TradeDirection['SHORT'] = 'SHORT';
})(TradeDirection || (TradeDirection = {}));
/**
 * Trade Status
 */
export var TradeStatus;
(function (TradeStatus) {
  TradeStatus['OPEN'] = 'OPEN';
  TradeStatus['CLOSED'] = 'CLOSED';
  TradeStatus['CANCELED'] = 'CANCELED';
  TradeStatus['REJECTED'] = 'REJECTED';
  TradeStatus['PENDING'] = 'PENDING';
})(TradeStatus || (TradeStatus = {}));
/**
 * Order Type
 */
export var OrderType;
(function (OrderType) {
  OrderType['MARKET'] = 'MARKET';
  OrderType['LIMIT'] = 'LIMIT';
  OrderType['STOP'] = 'STOP';
  OrderType['STOP_LIMIT'] = 'STOP_LIMIT';
})(OrderType || (OrderType = {}));
/**
 * Order Side
 */
export var OrderSide;
(function (OrderSide) {
  OrderSide['BUY'] = 'BUY';
  OrderSide['SELL'] = 'SELL';
})(OrderSide || (OrderSide = {}));
/**
 * Order Status
 */
export var OrderStatus;
(function (OrderStatus) {
  OrderStatus['PENDING'] = 'PENDING';
  OrderStatus['FILLED'] = 'FILLED';
  OrderStatus['PARTIALLY_FILLED'] = 'PARTIALLY_FILLED';
  OrderStatus['CANCELED'] = 'CANCELED';
  OrderStatus['REJECTED'] = 'REJECTED';
})(OrderStatus || (OrderStatus = {}));
/**
 * Time in Force
 */
export var TimeInForce;
(function (TimeInForce) {
  TimeInForce['GTC'] = 'GTC';
  TimeInForce['IOC'] = 'IOC';
  TimeInForce['FOK'] = 'FOK';
  TimeInForce['DAY'] = 'DAY';
})(TimeInForce || (TimeInForce = {}));
/**
 * Type Conversion Utilities
 */
export const TradeConverters = {
  /**
   * Convert CompleteTradeData to legacy Trade interface
   */
  completeTradeToLegacy: completeTradeData => {
    const trade = completeTradeData.trade;
    return {
      id: trade.id?.toString() || '0',
      symbol: trade.market || 'MNQ',
      date: trade.date,
      direction: trade.direction,
      size: trade.no_of_contracts || 1,
      entry: trade.entry_price || 0,
      exit: trade.exit_price || 0,
      stopLoss: 0, // Not in new schema
      takeProfit: 0, // Not in new schema
      profitLoss: trade.achieved_pl || 0,
      strategy: trade.setup || '',
      notes: trade.notes || '',
      tags: [], // Not in new schema
      images: [], // Not in new schema
    };
  },
  /**
   * Convert legacy Trade to CompleteTradeData
   */
  legacyToCompleteTrade: trade => {
    return {
      trade: {
        id: parseInt(trade.id) || undefined,
        date: trade.date,
        model_type: 'Unknown',
        direction: trade.direction,
        market: trade.symbol,
        entry_price: trade.entry,
        exit_price: trade.exit,
        achieved_pl: trade.profitLoss,
        no_of_contracts: trade.size,
        setup: trade.strategy,
        notes: trade.notes,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    };
  },
  /**
   * Convert array of CompleteTradeData to legacy Trade array
   */
  completeTradeArrayToLegacy: completeTrades => {
    return completeTrades.map(TradeConverters.completeTradeToLegacy);
  },
  /**
   * Convert array of legacy Trade to CompleteTradeData array
   */
  legacyArrayToCompleteTrade: trades => {
    return trades.map(TradeConverters.legacyToCompleteTrade);
  },
};
//# sourceMappingURL=trading.js.map
