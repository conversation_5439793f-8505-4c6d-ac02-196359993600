/**
 * Trading Sessions - Hierarchical Session System
 *
 * Provides a layered structure for trading sessions and macro periods:
 * - Broad sessions (NY AM, NY PM, London, etc.)
 * - Specific macro periods within each session
 * - Time validation and inheritance logic
 */
/**
 * Session type enumeration
 */
export var SessionType;
(function (SessionType) {
  // Major Sessions
  SessionType['LONDON'] = 'london';
  SessionType['NEW_YORK_AM'] = 'new-york-am';
  SessionType['NEW_YORK_PM'] = 'new-york-pm';
  SessionType['ASIA'] = 'asia';
  // Extended Sessions
  SessionType['PRE_MARKET'] = 'pre-market';
  SessionType['AFTER_HOURS'] = 'after-hours';
  SessionType['OVERNIGHT'] = 'overnight';
})(SessionType || (SessionType = {}));
/**
 * Macro period types
 */
export var MacroPeriodType;
(function (MacroPeriodType) {
  // Morning Macros
  MacroPeriodType['MORNING_BREAKOUT'] = 'morning-breakout';
  MacroPeriodType['MID_MORNING_REVERSION'] = 'mid-morning-reversion';
  // Lunch Macros
  MacroPeriodType['PRE_LUNCH'] = 'pre-lunch';
  MacroPeriodType['LUNCH_MACRO_EXTENDED'] = 'lunch-macro-extended';
  MacroPeriodType['LUNCH_MACRO'] = 'lunch-macro';
  MacroPeriodType['POST_LUNCH'] = 'post-lunch';
  // Afternoon Macros
  MacroPeriodType['PRE_CLOSE'] = 'pre-close';
  MacroPeriodType['POWER_HOUR'] = 'power-hour';
  MacroPeriodType['MOC'] = 'moc';
  // London Macros
  MacroPeriodType['LONDON_OPEN'] = 'london-open';
  MacroPeriodType['LONDON_NY_OVERLAP'] = 'london-ny-overlap';
  // Custom/Other
  MacroPeriodType['CUSTOM'] = 'custom';
})(MacroPeriodType || (MacroPeriodType = {}));
//# sourceMappingURL=tradingSessions.js.map
