#!/usr/bin/env node
/**
 * Theme CSS Generator Script
 *
 * Generates CSS variables from theme definitions and writes them to files.
 */
import { writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
// Import theme definitions
import { f1Theme } from '../src/theme/f1Theme.js';
import { f1OfficialTheme } from '../src/theme/f1OfficialTheme.js';
import { darkTheme } from '../src/theme/darkTheme.js';
import { generateAllThemeCSS } from '../src/theme/css-generator.js';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Define themes
const themes = {
  'mercedes-green': f1Theme,
  'f1-official': f1OfficialTheme,
  dark: darkTheme,
};
// Generate CSS
const css = generateAllThemeCSS(themes);
// Output paths
const outputDir = join(__dirname, '../../dashboard/src/styles/generated');
const outputFile = join(outputDir, 'theme-variables.css');
// Ensure output directory exists
mkdirSync(outputDir, { recursive: true });
// Write CSS file
writeFileSync(outputFile, css, 'utf8');
console.log(`✅ Generated theme CSS variables: ${outputFile}`);
console.log(`📊 Generated ${Object.keys(themes).length} themes`);
console.log(`📝 CSS file size: ${(css.length / 1024).toFixed(2)}KB`);
//# sourceMappingURL=generate-theme-css.js.map
